import 'dart:convert';
import 'user.dart';
import 'product.dart';

class Order {
  final String id;
  final String userId;
  final String productId;
  final String purpose;
  final DateTime fromDate;
  final DateTime toDate;
  final double rentalCost;
  final double securityDeposit;
  final double? discountCost;
  final String status;
  final String? accessories;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final User? user;
  final Product? product;

  Order({
    required this.id,
    required this.userId,
    required this.productId,
    required this.purpose,
    required this.fromDate,
    required this.toDate,
    required this.rentalCost,
    required this.securityDeposit,
    this.discountCost,
    required this.status,
    this.accessories,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.product,
  });

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id']?.toString() ?? '',
      userId: map['user_id']?.toString() ?? '',
      productId: map['product_id']?.toString() ?? '',
      purpose: map['purpose'] ?? '',
      fromDate: DateTime.parse(map['from']),
      toDate: DateTime.parse(map['to']),
      rentalCost: map['rental_cost']?.toDouble() ?? 0.0,
      securityDeposit: map['security_deposit']?.toDouble() ?? 0.0,
      discountCost: map['discount_cost']?.toDouble(),
      status: map['status'] ?? '',
      accessories: map['accessories'],
      createdAt: map['created_at'] != null ? DateTime.tryParse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.tryParse(map['updated_at']) : null,
      user: map['user'] != null ? User.fromMap(map['user']) : null,
      product: map['product'] != null ? Product.fromMap(map['product']) : null,
    );
  }

  factory Order.fromJson(String source) => Order.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'product_id': productId,
      'purpose': purpose,
      'from': fromDate.toIso8601String(),
      'to': toDate.toIso8601String(),
      'rental_cost': rentalCost,
      'security_deposit': securityDeposit,
      'discount_cost': discountCost,
      'status': status,
      'accessories': accessories,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'user': user?.toMap(),
      'product': product?.toMap(),
    };
  }

  String toJson() => json.encode(toMap());

  String get formattedRentalCost {
    return '₹${rentalCost.toStringAsFixed(0)}';
  }

  String get formattedSecurityDeposit {
    return '₹${securityDeposit.toStringAsFixed(0)}';
  }

  String get formattedDiscountCost {
    if (discountCost != null) {
      return '₹${discountCost!.toStringAsFixed(0)}';
    }
    return '₹0';
  }

  String get formattedFromDate {
    return '${fromDate.day}/${fromDate.month}/${fromDate.year}';
  }

  String get formattedToDate {
    return '${toDate.day}/${toDate.month}/${toDate.year}';
  }

  String get statusColor {
    switch (status.toLowerCase()) {
      case 'pending':
        return '#F59E0B';
      case 'confirmed':
        return '#3B82F6';
      case 'picked_up':
        return '#8B5CF6';
      case 'delivered':
        return '#10B981';
      case 'returned':
        return '#6B7280';
      case 'cancelled':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  }

  String get statusLabel {
    switch (status.toLowerCase()) {
      case 'picked_up':
        return 'Picked Up';
      case 'delivered':
        return 'Delivered';
      case 'returned':
        return 'Returned';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.toUpperCase();
    }
  }
}
