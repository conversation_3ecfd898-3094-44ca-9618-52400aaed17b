<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Str;

class User extends Authenticatable implements FilamentUser
{
    use HasFactory, Notifiable,HasUuids,SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'facebook_id',
        'twitter_id',
        'referral_code',
        'referral_earned',
        'firebase_token'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    protected static function boot(){
        parent::boot();
        static::creating(function ($user) {
            $user->referral_code = self::generateUniqueReferralCode();
        });
    }

    protected function name():Attribute
    {
        return Attribute::make(
            set: fn (string $value) => ucwords(strtolower($value)),
        );
    }

    public static function generateUniqueReferralCode(){
        do {
            $referralCode = Str::random(10);
        } while (self::where('referral_code', $referralCode)->exists());
        
        return $referralCode;
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function conversations()
    {
        return $this->belongsToMany(Conversation::class, 'conversation_participant');
    }

    public function messages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    public function referrals()
    {
        return $this->hasOne(Referral::class,'referee_id');
    }

    public function referrer(){
        return $this->hasMany(Referral::class,'referrer_id'); 
    }

    public function routeNotificationForFcm()
    {
        return $this->firebase_token;  // Return the device FCM token for the user
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return str_ends_with($this->email, '<EMAIL>');
    }
}
