<?php

namespace App\Filament\Resources\ProductAccessoriesResource\Pages;

use App\Filament\Resources\ProductAccessoriesResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Guava\FilamentNestedResources\Concerns\NestedPage;

class ListProductAccessories extends ListRecords
{
    use NestedPage;
    protected static string $resource = ProductAccessoriesResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
