import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'http://192.168.29.105:8000/api'; // Use 10.0.2.2 for Android emulator
  
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  static Future<Map<String, String>> _getHeaders({bool includeAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      final token = await _getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  static Future<Map<String, dynamic>> _handleResponse(http.Response response) async {
    try {
      final data = json.decode(response.body);

      // Check HTTP status code
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return data;
      } else {
        // Handle error responses
        return {
          'success': false,
          'message': data['message'] ?? 'Request failed',
          'errors': data['errors'] ?? null,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to parse response: ${e.toString()}',
      };
    }
  }

  // Auth endpoints
  static Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: await _getHeaders(includeAuth: false),
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  static Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/logout'),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Products endpoints
  static Future<Map<String, dynamic>> getProducts({Map<String, dynamic>? filters}) async {
    try {
      String url = '$baseUrl/products';
      if (filters != null && filters.isNotEmpty) {
        final queryParams = filters.entries
            .where((entry) => entry.value != null && entry.value.toString().isNotEmpty)
            .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
            .join('&');
        if (queryParams.isNotEmpty) {
          url += '?$queryParams';
        }
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  static Future<Map<String, dynamic>> createProduct(Map<String, dynamic> productData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/products'),
        headers: await _getHeaders(),
        body: json.encode(productData),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  static Future<Map<String, dynamic>> updateProduct(String id, Map<String, dynamic> productData) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/products/$id'),
        headers: await _getHeaders(),
        body: json.encode(productData),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  static Future<Map<String, dynamic>> deleteProduct(String id) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/products/$id'),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Users endpoints
  static Future<Map<String, dynamic>> getUsers({Map<String, dynamic>? filters}) async {
    try {
      String url = '$baseUrl/users';
      if (filters != null && filters.isNotEmpty) {
        final queryParams = filters.entries
            .where((entry) => entry.value != null && entry.value.toString().isNotEmpty)
            .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
            .join('&');
        if (queryParams.isNotEmpty) {
          url += '?$queryParams';
        }
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'message': 'Network error: ${e.toString()}',
      };
    }
  }

  // Orders endpoints
  static Future<Map<String, dynamic>> getOrders({Map<String, dynamic>? filters}) async {
    try {
      String url = '$baseUrl/orders';
      if (filters != null && filters.isNotEmpty) {
        final queryParams = filters.entries
            .where((entry) => entry.value != null && entry.value.toString().isNotEmpty)
            .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
            .join('&');
        if (queryParams.isNotEmpty) {
          url += '?$queryParams';
        }
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Vendors endpoints
  static Future<Map<String, dynamic>> getVendors({Map<String, dynamic>? filters}) async {
    try {
      String url = '$baseUrl/vendors';
      if (filters != null && filters.isNotEmpty) {
        final queryParams = filters.entries
            .where((entry) => entry.value != null && entry.value.toString().isNotEmpty)
            .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
            .join('&');
        if (queryParams.isNotEmpty) {
          url += '?$queryParams';
        }
      }

      final response = await http.get(
        Uri.parse(url),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Dashboard endpoints
  static Future<Map<String, dynamic>> getDashboardStats() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/dashboard/stats'),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
        'data': null,
      };
    }
  }

  // Timeline endpoints
  static Future<Map<String, dynamic>> getProductTimeline(String productId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/products/$productId/timeline'),
        headers: await _getHeaders(),
      );

      return await _handleResponse(response);
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error: ${e.toString()}',
        'data': null,
      };
    }
  }
}
