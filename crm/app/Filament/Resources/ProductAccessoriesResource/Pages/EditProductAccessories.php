<?php

namespace App\Filament\Resources\ProductAccessoriesResource\Pages;

use App\Filament\Resources\ProductAccessoriesResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Guava\FilamentNestedResources\Concerns\NestedPage;

class EditProductAccessories extends EditRecord
{
    use NestedPage;
    protected static string $resource = ProductAccessoriesResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
