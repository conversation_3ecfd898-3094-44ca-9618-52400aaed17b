<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Vendor;
use App\ProductVendorAssignment;
use Mr<PERSON><PERSON>em\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\ProductMaintenanceService;
use App\User;
use Laravel\Sanctum\Sanctum;

class VendorControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);
    }

    /** @test */
    public function it_can_get_all_vendors()
    {
        $vendors = Vendor::factory()->count(3)->create();

        $response = $this->getJson('/api/vendors');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'vendors' => [
                        '*' => [
                            'id',
                            'name',
                            'type',
                            'contact_person',
                            'phone',
                            'email',
                            'status'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('vendors'));
    }

    /** @test */
    public function it_can_assign_product_to_vendor()
    {
        $vendor = Vendor::factory()->create(['type' => 'dry_cleaner']);
        $product = Product::factory()->create();
        
        // Mark product as dirty first
        ProductMaintenanceService::updateStatusManually($product->id, 'dirty', 'Test', $this->user->id);

        $assignmentData = [
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'expected_return_date' => now()->addDays(3)->toDateString(),
            'cost' => 50.00,
            'work_description' => 'Deep cleaning required',
            'internal_notes' => 'Handle with care'
        ];

        $response = $this->postJson('/api/vendors/assign-product', $assignmentData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'assignment' => [
                        'id',
                        'product_id',
                        'vendor_id',
                        'assignment_type',
                        'status'
                    ]
                ]);

        // Check database
        $this->assertDatabaseHas('product_vendor_assignments', [
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned'
        ]);

        // Check product status was updated
        $latestStatus = ProductMaintenanceService::getCurrentStatus($product->id);
        $this->assertEquals('under_cleaning', $latestStatus['maintenance_status']);
    }

    /** @test */
    public function it_can_mark_product_as_returned()
    {
        $vendor = Vendor::factory()->create();
        $product = Product::factory()->create();

        // Create assignment
        $assignment = ProductVendorAssignment::create([
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $returnData = [
            'actual_return_date' => now()->toDateString(),
            'vendor_notes' => 'Cleaning completed successfully',
            'cost' => 45.00
        ];

        $response = $this->patchJson("/api/vendor-assignments/{$assignment->id}/return", $returnData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'assignment' => [
                        'id',
                        'status',
                        'actual_return_date',
                        'vendor_notes'
                    ]
                ]);

        // Check assignment was updated
        $assignment->refresh();
        $this->assertEquals('returned', $assignment->status);
        $this->assertEquals('Cleaning completed successfully', $assignment->vendor_notes);

        // Check product status was updated to clean
        $latestStatus = ProductMaintenanceService::getCurrentStatus($product->id);
        $this->assertEquals('ready', $latestStatus['maintenance_status']);
    }

    /** @test */
    public function it_can_get_products_with_vendors()
    {
        $vendor = Vendor::factory()->create(['name' => 'Test Vendor']);
        $product = Product::factory()->create(['name' => 'Test Product']);

        ProductVendorAssignment::create([
            'product_id' => $product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $response = $this->getJson('/api/products-with-vendors');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'assignments' => [
                        '*' => [
                            'id',
                            'product_id',
                            'vendor_id',
                            'assignment_type',
                            'status',
                            'assigned_date',
                            'vendor',
                            'product'
                        ]
                    ]
                ]);

        $assignments = $response->json('assignments');
        $this->assertCount(1, $assignments);
        $this->assertEquals('Test Product', $assignments[0]['product']['name']);
        $this->assertEquals('Test Vendor', $assignments[0]['vendor']['name']);
    }

    /** @test */
    public function it_prevents_assigning_already_assigned_product()
    {
        $vendor1 = Vendor::factory()->create();
        $vendor2 = Vendor::factory()->create();
        $product = Product::factory()->create();

        // First assignment
        ProductVendorAssignment::create([
            'product_id' => $product->id,
            'vendor_id' => $vendor1->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        // Try to assign to another vendor
        $assignmentData = [
            'product_id' => $product->id,
            'vendor_id' => $vendor2->id,
            'assignment_type' => 'repair',
        ];

        $response = $this->postJson('/api/vendors/assign-product', $assignmentData);

        $response->assertStatus(400)
                ->assertJson(['error' => true]);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        auth()->logout();

        $this->getJson('/api/vendors')->assertStatus(401);
        $this->postJson('/api/vendors', [])->assertStatus(401);
        $this->postJson('/api/vendors/assign-product', [])->assertStatus(401);
        $this->getJson('/api/products-with-vendors')->assertStatus(401);
    }
}
