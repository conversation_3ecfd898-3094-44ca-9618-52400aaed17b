<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Mr<PERSON><PERSON><PERSON>\LaradrobeServices\Enums\ProductStatus;
use Mr<PERSON><PERSON>em\LaradrobeServices\Enums\OrderStatus;
use Mr<PERSON><PERSON><PERSON>\LaradrobeServices\Enums\VendorAssignmentStatus;
use MrB<PERSON><PERSON>\LaradrobeServices\ProductMaintenanceService;
use MrBohem\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\Order;
use MrBohem\LaradrobeServices\Models\ProductVendorAssignment;
use Illuminate\Http\Request;

class StatusController extends Controller
{
    /**
     * Get all product statuses with metadata
     */
    public function getProductStatuses()
    {
        $statuses = [];

        foreach (ProductStatus::cases() as $status) {
            $statuses[] = [
                'value' => $status->value,
                'label' => $status->label(),
                'color' => $status->color(),
                'icon' => $status->icon(),
                'needs_attention' => $status->needsAttention(),
                'is_with_vendor' => $status->isWithVendor(),
                'is_available' => $status->isAvailable(),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $statuses,
            'categories' => [
                'needs_attention' => array_map(fn($s) => $s->value, ProductStatus::needsAttentionStatuses()),
                'with_vendor' => array_map(fn($s) => $s->value, ProductStatus::withVendorStatuses()),
                'available' => array_map(fn($s) => $s->value, ProductStatus::availableStatuses()),
            ]
        ]);
    }

    /**
     * Get maintenance status options (filtered for manual updates)
     */
    public function getMaintenanceStatusOptions()
    {
        // Only include statuses that can be manually set by admin (original system statuses)
        $maintenanceStatuses = [
            ProductStatus::READY,
            ProductStatus::NEEDS_CLEANING,
            ProductStatus::NEEDS_MAINTENANCE,
        ];

        $statuses = [];

        foreach ($maintenanceStatuses as $status) {
            $statuses[] = [
                'value' => $status->value,
                'label' => $status->label(),
                'color' => $status->color(),
                'icon' => $status->icon(),
                'needs_attention' => $status->needsAttention(),
                'is_with_vendor' => $status->isWithVendor(),
                'is_available' => $status->isAvailable(),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $statuses,
            'message' => 'Maintenance status options retrieved successfully'
        ]);
    }

    /**
     * Get all order statuses with metadata
     */
    public function getOrderStatuses()
    {
        $statuses = [];

        foreach (OrderStatus::cases() as $status) {
            $statuses[] = [
                'value' => $status->value,
                'label' => $status->label(),
                'color' => $status->color(),
                'icon' => $status->icon(),
                'is_active' => $status->isActive(),
                'can_be_cancelled' => $status->canBeCancelled(),
                'can_be_picked_up' => $status->canBePickedUp(),
                'can_be_delivered' => $status->canBeDelivered(),
                'can_be_returned' => $status->canBeReturned(),
                'next_statuses' => array_map(fn($s) => $s->value, $status->getNextStatuses()),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $statuses,
            'categories' => [
                'active' => array_map(fn($s) => $s->value, OrderStatus::activeStatuses()),
                'with_customer' => array_map(fn($s) => $s->value, OrderStatus::withCustomerStatuses()),
            ]
        ]);
    }

    /**
     * Get all vendor assignment statuses with metadata
     */
    public function getVendorAssignmentStatuses()
    {
        $statuses = [];

        foreach (VendorAssignmentStatus::cases() as $status) {
            $statuses[] = [
                'value' => $status->value,
                'label' => $status->label(),
                'color' => $status->color(),
                'icon' => $status->icon(),
                'is_active' => $status->isActive(),
                'can_be_cancelled' => $status->canBeCancelled(),
                'can_be_in_progress' => $status->canBeInProgress(),
                'can_be_completed' => $status->canBeCompleted(),
                'can_be_returned' => $status->canBeReturned(),
                'next_statuses' => array_map(fn($s) => $s->value, $status->getNextStatuses()),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $statuses,
            'categories' => [
                'active' => array_map(fn($s) => $s->value, VendorAssignmentStatus::activeStatuses()),
            ]
        ]);
    }

    /**
     * Get status counts for products
     */
    public function getProductStatusCounts()
    {
        $counts = [];

        foreach (ProductStatus::cases() as $status) {
            $productIds = ProductMaintenanceService::getProductsByStatus($status->value);
            $counts[$status->value] = [
                'count' => count($productIds),
                'label' => $status->label(),
                'color' => $status->color(),
            ];
        }

        // Add special categories
        $counts['needs_attention'] = [
            'count' => count(ProductMaintenanceService::getProductsByStatus('needs_attention')),
            'label' => 'Needs Attention',
            'color' => 'orange',
        ];

        $counts['with_vendor'] = [
            'count' => count(ProductMaintenanceService::getProductsByStatus('with_vendor')),
            'label' => 'With Vendor',
            'color' => 'blue',
        ];

        $counts['available'] = [
            'count' => count(ProductMaintenanceService::getProductsByStatus('clean_ready')),
            'label' => 'Available',
            'color' => 'green',
        ];

        return response()->json([
            'success' => true,
            'data' => $counts
        ]);
    }

    /**
     * Get status counts for orders
     */
    public function getOrderStatusCounts()
    {
        $counts = [];

        foreach (OrderStatus::cases() as $status) {
            $count = Order::where('status', $status->value)->count();
            $counts[$status->value] = [
                'count' => $count,
                'label' => $status->label(),
                'color' => $status->color(),
            ];
        }

        // Add special categories
        $counts['active'] = [
            'count' => Order::whereIn('status', array_map(fn($s) => $s->value, OrderStatus::activeStatuses()))->count(),
            'label' => 'Active Orders',
            'color' => 'blue',
        ];

        $counts['with_customer'] = [
            'count' => Order::whereIn('status', array_map(fn($s) => $s->value, OrderStatus::withCustomerStatuses()))->count(),
            'label' => 'With Customer',
            'color' => 'orange',
        ];

        return response()->json([
            'success' => true,
            'data' => $counts
        ]);
    }

    /**
     * Get status counts for vendor assignments
     */
    public function getVendorAssignmentStatusCounts()
    {
        $counts = [];

        foreach (VendorAssignmentStatus::cases() as $status) {
            $count = ProductVendorAssignment::where('status', $status->value)->count();
            $counts[$status->value] = [
                'count' => $count,
                'label' => $status->label(),
                'color' => $status->color(),
            ];
        }

        // Add special categories
        $counts['active'] = [
            'count' => ProductVendorAssignment::whereIn('status', array_map(fn($s) => $s->value, VendorAssignmentStatus::activeStatuses()))->count(),
            'label' => 'Active Assignments',
            'color' => 'blue',
        ];

        return response()->json([
            'success' => true,
            'data' => $counts
        ]);
    }

    /**
     * Get comprehensive status overview
     */
    public function getStatusOverview()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'products' => $this->getProductStatusCounts()->getData()->data,
                'orders' => $this->getOrderStatusCounts()->getData()->data,
                'vendor_assignments' => $this->getVendorAssignmentStatusCounts()->getData()->data,
            ]
        ]);
    }

    /**
     * Validate status transition
     */
    public function validateStatusTransition(Request $request)
    {
        $request->validate([
            'type' => 'required|in:product,order,vendor_assignment',
            'current_status' => 'required|string',
            'new_status' => 'required|string',
        ]);

        $type = $request->input('type');
        $currentStatus = $request->input('current_status');
        $newStatus = $request->input('new_status');

        $isValid = false;
        $allowedTransitions = [];

        switch ($type) {
            case 'product':
                // For products, most transitions are allowed (manual override)
                $isValid = ProductStatus::fromString($newStatus) !== null;
                $allowedTransitions = ProductStatus::values();
                break;

            case 'order':
                $current = OrderStatus::fromString($currentStatus);
                if ($current) {
                    $nextStatuses = $current->getNextStatuses();
                    $allowedTransitions = array_map(fn($s) => $s->value, $nextStatuses);
                    $isValid = in_array($newStatus, $allowedTransitions);
                }
                break;

            case 'vendor_assignment':
                $current = VendorAssignmentStatus::fromString($currentStatus);
                if ($current) {
                    $nextStatuses = $current->getNextStatuses();
                    $allowedTransitions = array_map(fn($s) => $s->value, $nextStatuses);
                    $isValid = in_array($newStatus, $allowedTransitions);
                }
                break;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'is_valid' => $isValid,
                'allowed_transitions' => $allowedTransitions,
                'current_status' => $currentStatus,
                'new_status' => $newStatus,
            ]
        ]);
    }
}
