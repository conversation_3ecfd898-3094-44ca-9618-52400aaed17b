// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const sidebar = document.querySelector('.sidebar');
    
    if (mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        }
    });
    
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add loading states to buttons
    document.querySelectorAll('.btn, .action-btn').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                
                // Remove loading state after 2 seconds (simulate API call)
                setTimeout(() => {
                    this.classList.remove('loading');
                    this.innerHTML = originalText;
                }, 2000);
            }
        });
    });
    
    // Add hover effects to cards
    document.querySelectorAll('.stat-card, .dashboard-card, .order-stat-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Animate numbers on page load
    function animateNumbers() {
        const numbers = document.querySelectorAll('.stat-number, .order-number');
        
        numbers.forEach(number => {
            const finalValue = parseInt(number.textContent.replace(/[^\d]/g, ''));
            let currentValue = 0;
            const increment = finalValue / 50;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                
                // Format the number based on original format
                const originalText = number.textContent;
                if (originalText.includes('₹')) {
                    if (finalValue >= 1000000) {
                        number.textContent = '₹' + (currentValue / 1000000).toFixed(1) + 'M';
                    } else if (finalValue >= 1000) {
                        number.textContent = '₹' + (currentValue / 1000).toFixed(1) + 'K';
                    } else {
                        number.textContent = '₹' + Math.floor(currentValue).toLocaleString();
                    }
                } else {
                    number.textContent = Math.floor(currentValue).toLocaleString();
                }
            }, 50);
        });
    }
    
    // Trigger number animation after a short delay
    setTimeout(animateNumbers, 500);
    
    // Add search functionality
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            // Here you would typically filter content based on search term
            console.log('Searching for:', searchTerm);
        });
    }
    
    // Add notification click handler
    const notificationBtn = document.querySelector('.header-btn .fa-bell').parentElement;
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            // Here you would typically show a notifications dropdown
            console.log('Notifications clicked');
        });
    }
    
    // Add settings click handler
    const settingsBtn = document.querySelector('.header-btn .fa-cog').parentElement;
    if (settingsBtn) {
        settingsBtn.addEventListener('click', function() {
            // Here you would typically show a settings dropdown
            console.log('Settings clicked');
        });
    }
    
    // Add user menu click handler
    const userMenu = document.querySelector('.user-menu');
    if (userMenu) {
        userMenu.addEventListener('click', function() {
            // Here you would typically show a user dropdown menu
            console.log('User menu clicked');
        });
    }
    
    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe all cards for scroll animations
    document.querySelectorAll('.stat-card, .dashboard-card, .order-stat-card, .activity-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Add CSS for loading state
const style = document.createElement('style');
style.textContent = `
    .btn.loading, .action-btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }
    
    .fa-spinner {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
