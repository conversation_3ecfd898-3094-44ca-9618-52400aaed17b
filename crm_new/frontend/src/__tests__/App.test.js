import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Simple test to verify testing setup works
describe('Basic Test Setup', () => {
  test('renders a simple div', () => {
    render(<div>Hello Testing</div>);
    expect(screen.getByText('Hello Testing')).toBeInTheDocument();
  });

  test('can perform basic assertions', () => {
    expect(1 + 1).toBe(2);
    expect('hello').toBe('hello');
    expect(true).toBeTruthy();
  });

  test('can mock functions', () => {
    const mockFn = jest.fn();
    mockFn('test');
    expect(mockFn).toHaveBeenCalledWith('test');
  });
});
