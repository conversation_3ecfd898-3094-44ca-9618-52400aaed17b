import 'dart:convert';

class Product {
  final String id;
  final String name;
  final String? description;
  final String? category;
  final String? size;
  final String? color;
  final String? feature;
  final String? brandName;
  final double? rentalCost;
  final double? securityDepositCost;
  final double? purchaseCost;
  final int? rentalDays;
  final String? thumbnail;
  final String? status;
  final int onStore;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ProductImage>? images;
  final List<ProductAccessory>? accessories;

  Product({
    required this.id,
    required this.name,
    this.description,
    this.category,
    this.size,
    this.color,
    this.feature,
    this.brandName,
    this.rentalCost,
    this.securityDepositCost,
    this.purchaseCost,
    this.rentalDays,
    this.thumbnail,
    this.status,
    this.onStore = 1,
    this.createdAt,
    this.updatedAt,
    this.images,
    this.accessories,
  });

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id']?.toString() ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      category: map['category'],
      size: map['size'],
      color: map['color'],
      feature: map['feature'],
      brandName: map['brand_name'],
      rentalCost: map['rental_cost']?.toDouble(),
      securityDepositCost: map['security_deposit_cost']?.toDouble(),
      purchaseCost: map['purchase_cost']?.toDouble(),
      rentalDays: map['rental_days']?.toInt(),
      thumbnail: map['thumbnail'],
      status: map['status'],
      onStore: map['on_store']?.toInt() ?? 1,
      createdAt: map['created_at'] != null ? DateTime.tryParse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.tryParse(map['updated_at']) : null,
      images: map['images'] != null 
          ? (map['images'] as List).map((x) => ProductImage.fromMap(x)).toList()
          : null,
      accessories: map['accessories'] != null 
          ? (map['accessories'] as List).map((x) => ProductAccessory.fromMap(x)).toList()
          : null,
    );
  }

  factory Product.fromJson(String source) => Product.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'size': size,
      'color': color,
      'feature': feature,
      'brand_name': brandName,
      'rental_cost': rentalCost,
      'security_deposit_cost': securityDepositCost,
      'purchase_cost': purchaseCost,
      'rental_days': rentalDays,
      'thumbnail': thumbnail,
      'status': status,
      'on_store': onStore,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'images': images?.map((x) => x.toMap()).toList(),
      'accessories': accessories?.map((x) => x.toMap()).toList(),
    };
  }

  String toJson() => json.encode(toMap());

  String get thumbnailUrl {
    if (thumbnail != null && thumbnail!.isNotEmpty) {
      return 'http://localhost:8001/storage/products/$thumbnail';
    }
    return '';
  }

  String get formattedRentalCost {
    if (rentalCost != null) {
      return '₹${rentalCost!.toStringAsFixed(0)}';
    }
    return '₹0';
  }

  String get formattedSecurityDeposit {
    if (securityDepositCost != null) {
      return '₹${securityDepositCost!.toStringAsFixed(0)}';
    }
    return '₹0';
  }

  bool get isAvailableOnStore {
    return onStore == 1;
  }

  String get availabilityLabel {
    return onStore == 1 ? 'Available in Store' : 'Not Available in Store';
  }

  String get statusLabel {
    switch (status?.toLowerCase()) {
      case 'available':
        return 'Available';
      case 'rented':
        return 'Rented';
      case 'maintenance':
        return 'Under Maintenance';
      case 'damaged':
        return 'Damaged';
      default:
        return status ?? 'Unknown';
    }
  }
}

class ProductImage {
  final String id;
  final String productId;
  final String filename;
  final bool? isThumbnail;
  final DateTime? createdAt;

  ProductImage({
    required this.id,
    required this.productId,
    required this.filename,
    this.isThumbnail,
    this.createdAt,
  });

  factory ProductImage.fromMap(Map<String, dynamic> map) {
    return ProductImage(
      id: map['id']?.toString() ?? '',
      productId: map['product_id']?.toString() ?? '',
      filename: map['filename'] ?? '',
      isThumbnail: map['is_thumbnail'],
      createdAt: map['created_at'] != null ? DateTime.tryParse(map['created_at']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'filename': filename,
      'is_thumbnail': isThumbnail,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  String get imageUrl {
    return 'http://localhost:8001/storage/products/$filename';
  }
}

class ProductAccessory {
  final String id;
  final String productId;
  final String name;
  final String? description;
  final DateTime? createdAt;

  ProductAccessory({
    required this.id,
    required this.productId,
    required this.name,
    this.description,
    this.createdAt,
  });

  factory ProductAccessory.fromMap(Map<String, dynamic> map) {
    return ProductAccessory(
      id: map['id']?.toString() ?? '',
      productId: map['product_id']?.toString() ?? '',
      name: map['name'] ?? '',
      description: map['description'],
      createdAt: map['created_at'] != null ? DateTime.tryParse(map['created_at']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'name': name,
      'description': description,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}
