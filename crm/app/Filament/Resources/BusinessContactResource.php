<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BusinessContactResource\Pages;
use App\Filament\Resources\BusinessContactResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Models\BusinessContact;

class BusinessContactResource extends Resource
{
    protected static ?string $model = BusinessContact::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationLabel = 'Business Contacts';

    protected static ?string $pluralLabel = 'Business Contacts';

    protected static ?string $slug = 'business-contacts';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Business Information')
                    ->schema([
                        Forms\Components\TextInput::make('business_name')
                            ->label('Business Name')
                            ->maxLength(255)
                            ->columnSpanFull(),
                        
                        Forms\Components\TextInput::make('business_owner_name')
                            ->label('Business Owner Name')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('business_phone')
                            ->label('Business Phone')
                            ->tel()
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Contact Details')
                    ->schema([
                        Forms\Components\Textarea::make('business_address')
                            ->label('Business Address')
                            ->rows(3)
                            ->columnSpanFull(),
                        
                        Forms\Components\Select::make('lead_source')
                            ->label('Lead Source')
                            ->options(BusinessContact::getLeadSourceOptions())
                            ->searchable(),
                        
                        Forms\Components\Select::make('interest_status')
                            ->label('Interest Status')
                            ->options(BusinessContact::getInterestStatusOptions())
                            ->default('not_confirmed')
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(4)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('business_name')
                    ->label('Business Name')
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('business_owner_name')
                    ->label('Owner')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('business_phone')
                    ->label('Phone')
                    ->searchable()
                    ->copyable()
                    ->toggleable(),

                Tables\Columns\SelectColumn::make('interest_status')
                    ->label('Status')
                    ->options(BusinessContact::getInterestStatusOptions())
                    ->sortable(),

                Tables\Columns\TextColumn::make('lead_source')
                    ->label('Lead Source')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'website' => 'success',
                        'referral' => 'info',
                        'social_media' => 'warning',
                        'cold_call' => 'danger',
                        default => 'gray',
                    })
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                ])
                ->filters([
                    Tables\Filters\SelectFilter::make('interest_status')
                        ->label('Interest Status')
                        ->options(BusinessContact::getInterestStatusOptions())
                        ->multiple(),

                    Tables\Filters\SelectFilter::make('lead_source')
                        ->label('Lead Source')
                        ->options(BusinessContact::getLeadSourceOptions())
                        ->multiple(),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->filters([
                //
            ])
            ->actions([
                 Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                   Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBusinessContacts::route('/'),
            'create' => Pages\CreateBusinessContact::route('/create'),
            'edit' => Pages\EditBusinessContact::route('/{record}/edit'),
            'view' => Pages\ViewBusinessContact::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['business_name', 'business_owner_name']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['business_name', 'business_owner_name', 'business_phone'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Owner' => $record->business_owner_name,
            'Phone' => $record->business_phone,
        ];
    }
}
