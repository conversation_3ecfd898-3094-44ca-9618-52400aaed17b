<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\Models\MasterCity;
use Illuminate\Http\Request;

class MasterCityController extends Controller
{
    public function index(Request $request)
    {
        $query = MasterCity::query();

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where('city', 'like', "%{$search}%");
        }

        // Sort
        $sortBy = $request->get('sort_by', 'order');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        if ($request->has('paginate') && $request->paginate === 'false') {
            $cities = $query->get();
        } else {
            $cities = $query->paginate($request->get('per_page', 15));
        }

        return response()->json($cities);
    }

    public function show(MasterCity $masterCity)
    {
        return response()->json([
            'city' => $masterCity
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'city' => 'required|string|max:255|unique:master_cities',
            'order' => 'required|integer|min:0',
        ]);

        $city = MasterCity::create($request->all());

        return response()->json([
            'city' => $city,
            'message' => 'City created successfully'
        ], 201);
    }

    public function update(Request $request, MasterCity $masterCity)
    {
        $request->validate([
            'city' => 'required|string|max:255|unique:master_cities,city,' . $masterCity->id,
            'order' => 'required|integer|min:0',
        ]);

        $masterCity->update($request->all());

        return response()->json([
            'city' => $masterCity,
            'message' => 'City updated successfully'
        ]);
    }

    public function destroy(MasterCity $masterCity)
    {
        $masterCity->delete();

        return response()->json([
            'message' => 'City deleted successfully'
        ]);
    }

    public function restore($id)
    {
        $city = MasterCity::withTrashed()->findOrFail($id);
        $city->restore();

        return response()->json([
            'city' => $city,
            'message' => 'City restored successfully'
        ]);
    }
}
