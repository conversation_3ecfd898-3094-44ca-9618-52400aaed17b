<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="LaravelPint">
    <laravel_pint_settings>
      <LaravelPintConfiguration tool_path="$PROJECT_DIR$/crm/vendor/bin/pint" />
    </laravel_pint_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/crm/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/crm/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/crm/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/crm/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/crm/vendor/guava/filament-nested-resources" />
      <path value="$PROJECT_DIR$/crm/vendor/anourvalar/eloquent-serialize" />
      <path value="$PROJECT_DIR$/crm/vendor/danharrin/livewire-rate-limiting" />
      <path value="$PROJECT_DIR$/crm/vendor/danharrin/date-format-converter" />
      <path value="$PROJECT_DIR$/crm/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/crm/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/crm/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/crm/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/crm/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/crm/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/crm/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/crm/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/crm/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/crm/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/crm/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/crm/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/crm/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/crm/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/crm/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/crm/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/crm/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/crm/vendor/blade-ui-kit/blade-heroicons" />
      <path value="$PROJECT_DIR$/crm/vendor/blade-ui-kit/blade-icons" />
      <path value="$PROJECT_DIR$/crm/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/crm/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/crm/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/crm/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/crm/vendor/openspout/openspout" />
      <path value="$PROJECT_DIR$/crm/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/crm/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/crm/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/crm/vendor/ryangjchandler/blade-capture-directive" />
      <path value="$PROJECT_DIR$/crm/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/crm/vendor/kirschbaum-development/eloquent-power-joins" />
      <path value="$PROJECT_DIR$/crm/vendor/spatie/invade" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/crm/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/crm/vendor/spatie/color" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/telescope" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/pail" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/crm/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/crm/vendor/intervention/image-laravel" />
      <path value="$PROJECT_DIR$/crm/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/crm/vendor/intervention/gif" />
      <path value="$PROJECT_DIR$/crm/vendor/mrbohem/laradrobe-services" />
      <path value="$PROJECT_DIR$/crm/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/crm/vendor/mrbohem/laramage" />
      <path value="$PROJECT_DIR$/crm/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/crm/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/crm/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/crm/vendor/composer" />
      <path value="$PROJECT_DIR$/crm/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/crm/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/crm/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/forms" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/actions" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/support" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/widgets" />
      <path value="$PROJECT_DIR$/crm/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/tables" />
      <path value="$PROJECT_DIR$/crm/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/notifications" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/infolists" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/crm/vendor/filament/filament" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/crm/vendor/league/config" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/crm/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/crm/vendor/league/flysystem-aws-s3-v3" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/crm/vendor/league/uri" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/html-sanitizer" />
      <path value="$PROJECT_DIR$/crm/vendor/league/csv" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/crm/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/crm/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/crm/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/crm/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/crm/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/container" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/crm/vendor/psr/log" />
      <path value="$PROJECT_DIR$/crm/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/crm/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/crm/vendor/staabm/side-effects-detector" />
      <path value="$PROJECT_DIR$/crm/vendor/livewire/livewire" />
      <path value="$PROJECT_DIR$/crm/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/crm/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/crm/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/crm/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/crm/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/crm/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/crm/vendor/brick/math" />
      <path value="$PROJECT_DIR$/crm/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/crm/vendor/masterminds/html5" />
      <path value="$PROJECT_DIR$/crm/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/laravel/vendor/grpc/grpc" />
      <path value="$PROJECT_DIR$/laravel/vendor/ta-tikoma/phpunit-architecture-test" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/telescope" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/reverb" />
      <path value="$PROJECT_DIR$/laravel/vendor/firebase/php-jwt" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/pulse" />
      <path value="$PROJECT_DIR$/laravel/vendor/rize/uri-template" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel/socialite" />
      <path value="$PROJECT_DIR$/laravel/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/laravel/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/laravel/vendor/pusher/pusher-php-server" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/laravel/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/laravel/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/laravel/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/laravel/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/laravel/vendor/composer" />
      <path value="$PROJECT_DIR$/laravel/vendor/fig/http-message-util" />
      <path value="$PROJECT_DIR$/laravel/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/oauth1-client" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/config" />
      <path value="$PROJECT_DIR$/laravel/vendor/laravel-notification-channels/fcm" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/container" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/uri" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/flysystem-aws-s3-v3" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/laravel/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/laravel/vendor/psr/log" />
      <path value="$PROJECT_DIR$/laravel/vendor/aws/aws-crt-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/aws/aws-sdk-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/laravel/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/laravel/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/laravel/vendor/ratchet/rfc6455" />
      <path value="$PROJECT_DIR$/laravel/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/laravel/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/laravel/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/laravel/vendor/lcobucci/jwt" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/promise" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/stream" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/dns" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/socket" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/promise-timer" />
      <path value="$PROJECT_DIR$/laravel/vendor/barryvdh/laravel-debugbar" />
      <path value="$PROJECT_DIR$/laravel/vendor/react/event-loop" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/cache-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/var-exporter" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/evenement/evenement" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/fruitcake/php-cors" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/temporary-directory" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-ignition" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/backtrace" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-signal-aware-command" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/db-dumper" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/ignition" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/laravel/vendor/mtdowling/jmespath.php" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/flare-client-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/error-solutions" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/laravel/vendor/spatie/laravel-backup" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/laravel/vendor/jean85/pretty-package-versions" />
      <path value="$PROJECT_DIR$/laravel/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/laravel/vendor/beste/clock" />
      <path value="$PROJECT_DIR$/laravel/vendor/beste/in-memory-cache" />
      <path value="$PROJECT_DIR$/laravel/vendor/beste/json" />
      <path value="$PROJECT_DIR$/laravel/vendor/kreait/laravel-firebase" />
      <path value="$PROJECT_DIR$/laravel/vendor/paragonie/constant_time_encoding" />
      <path value="$PROJECT_DIR$/laravel/vendor/kreait/firebase-php" />
      <path value="$PROJECT_DIR$/laravel/vendor/paragonie/sodium_compat" />
      <path value="$PROJECT_DIR$/laravel/vendor/kreait/firebase-tokens" />
      <path value="$PROJECT_DIR$/laravel/vendor/paragonie/random_compat" />
      <path value="$PROJECT_DIR$/laravel/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/laravel/vendor/php-debugbar/php-debugbar" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/laravel/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/laravel/vendor/brianium/paratest" />
      <path value="$PROJECT_DIR$/laravel/vendor/livewire/livewire" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/laravel/vendor/livewire/volt" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/laravel/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/laravel/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/laravel/vendor/brick/math" />
      <path value="$PROJECT_DIR$/laravel/vendor/fidry/cpu-core-counter" />
      <path value="$PROJECT_DIR$/laravel/vendor/mrbohem/laramage" />
      <path value="$PROJECT_DIR$/laravel/vendor/mrbohem/laradrobe-services" />
      <path value="$PROJECT_DIR$/laravel/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/laravel/vendor/intervention/image-laravel" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/cloud-storage" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/laravel/vendor/intervention/gif" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/longrunning" />
      <path value="$PROJECT_DIR$/laravel/vendor/intervention/image" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/auth" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/sql-formatter" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/common-protos" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/grpc-gcp" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/gax" />
      <path value="$PROJECT_DIR$/laravel/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/protobuf" />
      <path value="$PROJECT_DIR$/laravel/vendor/google/cloud-core" />
      <path value="$PROJECT_DIR$/laravel/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/laravel/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/laravel/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/laravel/vendor/abraham/twitteroauth" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpseclib/phpseclib" />
      <path value="$PROJECT_DIR$/laravel/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/laravel/vendor/clue/redis-react" />
      <path value="$PROJECT_DIR$/laravel/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/laravel/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/laravel/vendor/clue/redis-protocol" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/laravel/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/laravel/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/laravel/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/laravel/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/laravel/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/laravel/vendor/pestphp/pest" />
      <path value="$PROJECT_DIR$/laravel/vendor/pestphp/pest-plugin" />
      <path value="$PROJECT_DIR$/laravel/vendor/pestphp/pest-plugin-arch" />
      <path value="$PROJECT_DIR$/laravel/vendor/pestphp/pest-plugin-laravel" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.2" />
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PhpUnit">
    <phpunit_settings>
      <PhpUnitSettings configuration_file_path="$PROJECT_DIR$/crm/phpunit.xml" custom_loader_path="$PROJECT_DIR$/crm/vendor/autoload.php" use_configuration_file="true" />
    </phpunit_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>