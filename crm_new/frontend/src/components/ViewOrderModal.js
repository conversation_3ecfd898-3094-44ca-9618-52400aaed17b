import React, { useState, useEffect } from 'react';
import { X, Package, User, Calendar, MapPin, Phone, Mail, CreditCard, Tag, Clock } from 'lucide-react';
import orderAPI from '../services/orderAPI';

const ViewOrderModal = ({ isOpen, onClose, orderId }) => {
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen && orderId) {
      fetchOrderDetails();
    }
  }, [isOpen, orderId]);

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await orderAPI.getOrder(orderId);
      setOrder(response.order);
    } catch (error) {
      console.error('Error fetching order details:', error);
      setError('Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return { bg: '#fef3c7', text: '#92400e', border: '#fcd34d' };
      case 'confirmed':
        return { bg: '#dbeafe', text: '#1e40af', border: '#93c5fd' };
      case 'picked_up':
        return { bg: '#e0e7ff', text: '#3730a3', border: '#a5b4fc' };
      case 'delivered':
        return { bg: '#dcfce7', text: '#166534', border: '#86efac' };
      case 'returned':
        return { bg: '#f3f4f6', text: '#374151', border: '#d1d5db' };
      case 'cancelled':
        return { bg: '#fee2e2', text: '#dc2626', border: '#fca5a5' };
      default:
        return { bg: '#f3f4f6', text: '#6b7280', border: '#d1d5db' };
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return `₹${(amount || 0).toLocaleString()}`;
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Package size={24} color="#3b82f6" />
            <div>
              <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600', color: '#1f2937' }}>
                Order Details
              </h2>
              <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                {orderId ? `Order #${orderId}` : 'Loading...'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            style={{
              background: 'none',
              border: 'none',
              cursor: loading ? 'not-allowed' : 'pointer',
              padding: '4px',
              borderRadius: '6px',
              color: '#6b7280'
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '20px'
        }}>
          {loading && (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px',
              flexDirection: 'column',
              gap: '16px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                border: '4px solid #f3f4f6',
                borderTop: '4px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              <p style={{ color: '#6b7280', fontSize: '16px' }}>Loading order details...</p>
            </div>
          )}

          {error && (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px',
              flexDirection: 'column',
              gap: '16px'
            }}>
              <Package size={48} color="#ef4444" />
              <div style={{ textAlign: 'center' }}>
                <p style={{ color: '#ef4444', fontSize: '18px', fontWeight: '600', margin: '0 0 8px 0' }}>
                  Error Loading Order
                </p>
                <p style={{ color: '#6b7280', fontSize: '14px', margin: '0 0 16px 0' }}>
                  {error}
                </p>
                <button
                  onClick={fetchOrderDetails}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {order && (
            <div style={{ display: 'grid', gap: '24px' }}>
              {/* Order Status */}
              <div style={{
                padding: '16px',
                backgroundColor: '#f9fafb',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <div>
                  <h3 style={{ margin: '0 0 4px 0', fontSize: '16px', fontWeight: '600', color: '#1f2937' }}>
                    Order Status
                  </h3>
                  <p style={{ margin: 0, fontSize: '14px', color: '#6b7280' }}>
                    Current status of this order
                  </p>
                </div>
                <div style={{
                  ...getStatusColor(order.status),
                  padding: '8px 16px',
                  borderRadius: '20px',
                  border: `1px solid ${getStatusColor(order.status).border}`,
                  fontSize: '14px',
                  fontWeight: '600',
                  textTransform: 'capitalize'
                }}>
                  {order.status?.replace('_', ' ') || 'Unknown'}
                </div>
              </div>

              {/* Customer Information */}
              <div style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}>
                <h3 style={{
                  margin: '0 0 16px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <User size={18} />
                  Customer Information
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Name
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {order.user?.profile?.name || order.user?.name || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Email
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '6px' }}>
                      <Mail size={14} />
                      {order.user?.email || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Phone
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '6px' }}>
                      <Phone size={14} />
                      {order.user?.profile?.phone || order.user?.phone || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Address
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937', display: 'flex', alignItems: 'center', gap: '6px' }}>
                      <MapPin size={14} />
                      {order.user?.profile?.address || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Product Information */}
              <div style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}>
                <h3 style={{
                  margin: '0 0 16px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <Package size={18} />
                  Product Information
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Product Name
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937', fontWeight: '600' }}>
                      {order.product?.name || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Product Owner
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {order.product?.user?.profile?.name || order.product?.user?.name || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Purpose
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937', textTransform: 'capitalize' }}>
                      {order.purpose || 'N/A'}
                    </p>
                  </div>
                  {order.accessories && Array.isArray(order.accessories) && order.accessories.length > 0 && (
                    <div>
                      <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                        Accessories
                      </label>
                      <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                        {order.accessories.length} items included
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Rental Period */}
              <div style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}>
                <h3 style={{
                  margin: '0 0 16px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <Calendar size={18} />
                  Rental Period
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      From Date
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {formatDate(order.from)}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      To Date
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {formatDate(order.to)}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Duration
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {order.from && order.to ? 
                        Math.ceil((new Date(order.to) - new Date(order.from)) / (1000 * 60 * 60 * 24)) + ' days' : 
                        'N/A'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Financial Information */}
              <div style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}>
                <h3 style={{
                  margin: '0 0 16px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <CreditCard size={18} />
                  Financial Details
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Rental Cost
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '16px', color: '#059669', fontWeight: '600' }}>
                      {formatCurrency(order.rental_cost)}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Security Deposit
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '16px', color: '#1f2937', fontWeight: '600' }}>
                      {formatCurrency(order.security_deposit_cost)}
                    </p>
                  </div>
                  {order.discount_cost > 0 && (
                    <div>
                      <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                        Discount
                      </label>
                      <p style={{ margin: '4px 0 0 0', fontSize: '16px', color: '#ef4444', fontWeight: '600' }}>
                        -{formatCurrency(order.discount_cost)}
                      </p>
                    </div>
                  )}
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Total Amount
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '18px', color: '#059669', fontWeight: '700' }}>
                      {formatCurrency((order.rental_cost || 0) + (order.security_deposit_cost || 0) - (order.discount_cost || 0))}
                    </p>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}>
                <h3 style={{
                  margin: '0 0 16px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <Tag size={18} />
                  Additional Information
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Clean Required
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: order.is_clean ? '#059669' : '#ef4444', fontWeight: '600' }}>
                      {order.is_clean ? 'Yes' : 'No'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Cover Required
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: order.has_cover ? '#059669' : '#ef4444', fontWeight: '600' }}>
                      {order.has_cover ? 'Yes' : 'No'}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Hanger Required
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: order.has_hanger ? '#059669' : '#ef4444', fontWeight: '600' }}>
                      {order.has_hanger ? 'Yes' : 'No'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Order Timeline */}
              <div style={{
                padding: '16px',
                border: '1px solid #e5e7eb',
                borderRadius: '8px'
              }}>
                <h3 style={{
                  margin: '0 0 16px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <Clock size={18} />
                  Order Timeline
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Order Created
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {formatDate(order.created_at)}
                    </p>
                  </div>
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Last Updated
                    </label>
                    <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937' }}>
                      {formatDate(order.updated_at)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div style={{
          padding: '20px',
          borderTop: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'flex-end'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '10px 20px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              backgroundColor: 'white',
              color: '#374151',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            Close
          </button>
        </div>
      </div>

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default ViewOrderModal;
