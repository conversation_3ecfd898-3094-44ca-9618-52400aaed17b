<?php

namespace App\Filament\Resources\BusinessContactResource\Pages;

use App\Filament\Resources\BusinessContactResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBusinessContact extends EditRecord
{
    protected static string $resource = BusinessContactResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
