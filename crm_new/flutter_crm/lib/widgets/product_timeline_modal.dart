import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/product.dart';
import '../services/api_service.dart';
import '../utils/app_theme.dart';

class ProductTimelineModal extends StatefulWidget {
  final Product product;

  const ProductTimelineModal({
    super.key,
    required this.product,
  });

  @override
  State<ProductTimelineModal> createState() => _ProductTimelineModalState();
}

class _ProductTimelineModalState extends State<ProductTimelineModal> {
  List<TimelineEvent> _timeline = [];
  Map<String, dynamic> _stats = {};
  bool _loading = true;
  String? _error;
  String _filterType = 'all';

  final List<Map<String, String>> _eventTypes = [
    {'value': 'all', 'label': 'All Events'},
    {'value': 'product_registered', 'label': 'Registration'},
    {'value': 'order_created', 'label': 'Orders'},
    {'value': 'return_processed', 'label': 'Returns'},
    {'value': 'maintenance_status', 'label': 'Maintenance'},
    {'value': 'vendor_assigned', 'label': 'Vendor Work'},
  ];

  @override
  void initState() {
    super.initState();
    _loadTimeline();
  }

  Future<void> _loadTimeline() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final response = await ApiService.getProductTimeline(widget.product.id);

      if (response['success'] == true && response['data'] != null) {
        // Success response with nested data structure
        final data = response['data'];
        print(data);
        final timelineData = data['timeline'] as List? ?? [];
        _timeline = timelineData.map((json) => TimelineEvent.fromMap(json)).toList();
        _stats = data['stats'] ?? {};
      } else {
        _error = response['message'] ?? 'Failed to load timeline';
      }
    } catch (e) {
      _error = 'Error loading timeline: ${e.toString()}';
    }

    setState(() {
      _loading = false;
    });
  }

  List<TimelineEvent> get _filteredTimeline {
    if (_filterType == 'all') return _timeline;
    return _timeline.where((event) => event.type == _filterType).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: AppTheme.gray200)),
              ),
              child: Row(
                children: [
                  if (widget.product.thumbnailUrl.isNotEmpty)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: SizedBox(
                        width: 48,
                        height: 48,
                        child: Image.network(
                          widget.product.thumbnailUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            color: AppTheme.gray200,
                            child: const Icon(Icons.broken_image, color: AppTheme.gray400),
                          ),
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: AppTheme.gray200,
                              child: const Icon(Icons.image, color: AppTheme.gray400),
                            );
                          },
                        ),
                      ),
                    ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Product Timeline',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.gray900,
                          ),
                        ),
                        Text(
                          widget.product.name,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.gray600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Stats Bar
            if (_stats.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                color: AppTheme.gray50,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Expanded(child: _buildStatItem('Total Events', _stats['total_events']?.toString() ?? '0')),
                    Expanded(child: _buildStatItem('Orders', _stats['total_orders']?.toString() ?? '0')),
                    Expanded(child: _buildStatItem('Returns', _stats['total_returns']?.toString() ?? '0')),
                    Expanded(child: _buildStatItem('Vendor Work', _stats['vendor_assignments']?.toString() ?? '0')),
                  ],
                ),
              ),

            // Filter Bar
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: AppTheme.gray200)),
              ),
              child: Row(
                children: [
                  const Text(
                    'Filter:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.gray700,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButton<String>(
                      value: _filterType,
                      isExpanded: true,
                      underline: const SizedBox(),
                      onChanged: (value) {
                        setState(() {
                          _filterType = value!;
                        });
                      },
                      items: _eventTypes.map((type) {
                        return DropdownMenuItem<String>(
                          value: type['value'],
                          child: Text(type['label']!),
                        );
                      }).toList(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '${_filteredTimeline.length} events',
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.gray600,
                    ),
                  ),
                ],
              ),
            ),

            // Timeline Content
            Expanded(
              child: _loading
                  ? const Center(child: CircularProgressIndicator())
                  : _error != null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.error_outline,
                                size: 48,
                                color: AppTheme.error,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _error!,
                                style: TextStyle(color: AppTheme.error),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadTimeline,
                                child: const Text('Retry'),
                              ),
                            ],
                          ),
                        )
                      : _filteredTimeline.isEmpty
                          ? const Center(
                              child: Text(
                                'No timeline events found',
                                style: TextStyle(color: AppTheme.gray600),
                              ),
                            )
                          : _buildTimelineList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: AppTheme.gray900,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppTheme.gray600,
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineList() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: _filteredTimeline.length,
      itemBuilder: (context, index) {
        final event = _filteredTimeline[index];
        final isLast = index == _filteredTimeline.length - 1;
        
        return _buildTimelineItem(event, isLast);
      },
    );
  }

  Widget _buildTimelineItem(TimelineEvent event, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: _getEventColor(event.type),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.gray300,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Icon(
                _getEventIcon(event.type),
                size: 12,
                color: Colors.white,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 60,
                color: AppTheme.gray300,
              ),
          ],
        ),
        const SizedBox(width: 16),

        // Event content
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppTheme.gray200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      event.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.gray900,
                      ),
                    ),
                    Text(
                      event.formattedDate,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.gray600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  event.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.gray600,
                  ),
                ),
                if (event.data.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.gray50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      children: event.data.entries.map((entry) {
                        if (entry.value == null || entry.value.toString().isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  _formatFieldName(entry.key),
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: AppTheme.gray600,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 3,
                                child: Text(
                                  entry.value.toString(),
                                  style: const TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.w500,
                                    color: AppTheme.gray700,
                                  ),
                                  textAlign: TextAlign.right,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getEventColor(String type) {
    switch (type) {
      case 'product_registered':
        return AppTheme.success;
      case 'order_created':
        return AppTheme.primary;
      case 'order_pickup':
        return AppTheme.warning;
      case 'order_delivery':
        return AppTheme.success;
      case 'order_returned':
        return AppTheme.gray600;
      case 'return_processed':
        return const Color(0xFF8B5CF6);
      case 'maintenance_status':
        return AppTheme.warning;
      case 'vendor_assigned':
        return AppTheme.warning;
      case 'vendor_returned':
        return AppTheme.success;
      default:
        return AppTheme.gray600;
    }
  }

  IconData _getEventIcon(String type) {
    switch (type) {
      case 'product_registered':
        return Icons.inventory_2;
      case 'order_created':
        return Icons.shopping_cart;
      case 'order_pickup':
        return Icons.local_shipping;
      case 'order_delivery':
        return Icons.check_circle;
      case 'order_returned':
        return Icons.keyboard_return;
      case 'return_processed':
        return Icons.assignment_turned_in;
      case 'maintenance_status':
        return Icons.build;
      case 'vendor_assigned':
        return Icons.person_add;
      case 'vendor_returned':
        return Icons.person_remove;
      default:
        return Icons.timeline;
    }
  }

  String _formatFieldName(String fieldName) {
    return fieldName
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }
}

class TimelineEvent {
  final String id;
  final String type;
  final String title;
  final String description;
  final DateTime date;
  final String icon;
  final String color;
  final Map<String, dynamic> data;

  TimelineEvent({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.date,
    required this.icon,
    required this.color,
    required this.data,
  });

  factory TimelineEvent.fromMap(Map<String, dynamic> map) {
    return TimelineEvent(
      id: map['id']?.toString() ?? '',
      type: map['type'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      date: DateTime.parse(map['date']),
      icon: map['icon'] ?? '',
      color: map['color'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
    );
  }

  String get formattedDate {
    return DateFormat('MMM d, y').format(date);
  }

  String get formattedTime {
    return DateFormat('h:mm a').format(date);
  }
}
