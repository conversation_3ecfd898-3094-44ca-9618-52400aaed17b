<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laradrobe CRM - Login</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="login-styles.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <!-- Left Side - Branding -->
        <div class="login-branding">
            <div class="branding-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <h1>Laradrobe CRM</h1>
                    <p>Manage your fashion rental business with ease and style</p>
                </div>
                
                <div class="stats-showcase">
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">Customers</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Cities</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Support</div>
                    </div>
                </div>
                
                <div class="testimonial">
                    <div class="testimonial-content">
                        <i class="fas fa-quote-left"></i>
                        <p>"Laradrobe CRM has transformed how we manage our fashion rental business. The interface is intuitive and the features are exactly what we needed."</p>
                        <div class="testimonial-author">
                            <div class="author-avatar">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face" alt="Sarah Johnson">
                            </div>
                            <div class="author-info">
                                <div class="author-name">Sarah Johnson</div>
                                <div class="author-title">CEO, Fashion Forward</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Decorative Elements -->
            <div class="decoration decoration-1"></div>
            <div class="decoration decoration-2"></div>
            <div class="decoration decoration-3"></div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="login-form-section">
            <div class="form-container">
                <!-- Mobile Logo -->
                <div class="mobile-logo">
                    <div class="logo-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <h2>Laradrobe CRM</h2>
                </div>
                
                <div class="login-form-card">
                    <div class="form-header">
                        <h3>Welcome Back!</h3>
                        <p>Sign in to access your admin dashboard</p>
                    </div>
                    
                    <div class="error-message" id="errorMessage" style="display: none;">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>Invalid email or password. Please try again.</span>
                    </div>
                    
                    <form class="login-form" id="loginForm">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="password" name="password" placeholder="Enter your password" required>
                                <button type="button" class="password-toggle" id="passwordToggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-wrapper">
                                <input type="checkbox" id="remember">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password">Forgot password?</a>
                        </div>
                        
                        <button type="submit" class="login-btn" id="loginBtn">
                            <span class="btn-text">Sign In</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                                Signing in...
                            </span>
                        </button>
                    </form>
                    
                    <div class="demo-credentials">
                        <div class="demo-header">
                            <i class="fas fa-info-circle"></i>
                            Demo Credentials
                        </div>
                        <div class="demo-content">
                            <div class="demo-item">
                                <strong>Email:</strong> <EMAIL>
                            </div>
                            <div class="demo-item">
                                <strong>Password:</strong> password
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-footer">
                    <p>&copy; 2024 Laradrobe CRM. All rights reserved.</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Login form functionality
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            const passwordToggle = document.getElementById('passwordToggle');
            const passwordInput = document.getElementById('password');
            
            // Password toggle functionality
            passwordToggle.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
            
            // Form submission
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                // Hide error message
                errorMessage.style.display = 'none';
                
                // Show loading state
                const btnText = loginBtn.querySelector('.btn-text');
                const btnLoading = loginBtn.querySelector('.btn-loading');
                
                btnText.style.display = 'none';
                btnLoading.style.display = 'flex';
                loginBtn.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    if (email === '<EMAIL>' && password === 'password') {
                        // Success - redirect to dashboard
                        window.location.href = 'index.html';
                    } else {
                        // Error - show error message
                        errorMessage.style.display = 'flex';
                        
                        // Reset button state
                        btnText.style.display = 'block';
                        btnLoading.style.display = 'none';
                        loginBtn.disabled = false;
                    }
                }, 2000);
            });
            
            // Auto-fill demo credentials when clicking on them
            document.querySelectorAll('.demo-item').forEach(item => {
                item.addEventListener('click', function() {
                    const text = this.textContent;
                    if (text.includes('<EMAIL>')) {
                        document.getElementById('email').value = '<EMAIL>';
                    } else if (text.includes('password')) {
                        document.getElementById('password').value = 'password';
                    }
                });
            });
            
            // Add floating animation to decorative elements
            const decorations = document.querySelectorAll('.decoration');
            decorations.forEach((decoration, index) => {
                decoration.style.animationDelay = `${index * 0.5}s`;
            });
        });
    </script>
</body>
</html>
