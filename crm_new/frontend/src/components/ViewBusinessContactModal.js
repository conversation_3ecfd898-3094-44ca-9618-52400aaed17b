import React from 'react';
import { X, Building, User, Phone, MapPin, FileText, Tag, Globe, Calendar, Clock } from 'lucide-react';

const ViewBusinessContactModal = ({ 
  isOpen, 
  onClose, 
  contact,
  interestStatusOptions = {}, 
  leadSourceOptions = {} 
}) => {
  if (!isOpen || !contact) return null;

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'not contacted': '#6b7280',
      'not_confirmed': '#f59e0b',
      'interested': '#10b981',
      'not_interested': '#ef4444',
      'follow_up': '#3b82f6',
      'converted': '#8b5cf6'
    };
    return colors[status] || '#6b7280';
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'hidden',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{
          padding: '24px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              backgroundColor: '#8b5cf6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Building size={20} style={{ color: 'white' }} />
            </div>
            <div>
              <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600', color: '#1f2937' }}>
                Business Contact Details
              </h2>
              <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#6b7280' }}>
                View contact information and status
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '6px',
              color: '#6b7280'
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div style={{ padding: '24px', overflowY: 'auto', maxHeight: 'calc(90vh - 140px)' }}>
          <div style={{ display: 'grid', gap: '24px' }}>
            {/* Business Information */}
            <div style={{
              padding: '20px',
              backgroundColor: '#f8fafc',
              borderRadius: '8px',
              border: '1px solid #e2e8f0'
            }}>
              <h3 style={{
                margin: '0 0 16px 0',
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <Building size={18} />
                Business Information
              </h3>
              
              <div style={{ display: 'grid', gap: '12px' }}>
                <div>
                  <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                    Business Name
                  </label>
                  <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#1f2937', fontWeight: '500' }}>
                    {contact.business_name || 'Not specified'}
                  </p>
                </div>

                {contact.business_owner_name && (
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Business Owner
                    </label>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      fontSize: '14px', 
                      color: '#1f2937',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <User size={16} style={{ color: '#6b7280' }} />
                      {contact.business_owner_name}
                    </p>
                  </div>
                )}

                {contact.business_phone && (
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Phone Number
                    </label>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      fontSize: '14px', 
                      color: '#1f2937',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <Phone size={16} style={{ color: '#6b7280' }} />
                      <a href={`tel:${contact.business_phone}`} style={{ color: '#3b82f6', textDecoration: 'none' }}>
                        {contact.business_phone}
                      </a>
                    </p>
                  </div>
                )}

                {contact.business_address && (
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Address
                    </label>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      fontSize: '14px', 
                      color: '#1f2937',
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '8px'
                    }}>
                      <MapPin size={16} style={{ color: '#6b7280', marginTop: '2px' }} />
                      {contact.business_address}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Lead Information */}
            <div style={{
              padding: '20px',
              backgroundColor: '#f0f9ff',
              borderRadius: '8px',
              border: '1px solid #bae6fd'
            }}>
              <h3 style={{
                margin: '0 0 16px 0',
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <Tag size={18} />
                Lead Information
              </h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                    Interest Status
                  </label>
                  <div style={{ margin: '4px 0 0 0' }}>
                    <span style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      padding: '6px 12px',
                      borderRadius: '6px',
                      fontSize: '14px',
                      fontWeight: '500',
                      backgroundColor: `${getStatusColor(contact.interest_status)}20`,
                      color: getStatusColor(contact.interest_status)
                    }}>
                      {interestStatusOptions[contact.interest_status] || contact.interest_status || 'Not specified'}
                    </span>
                  </div>
                </div>

                <div>
                  <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                    Lead Source
                  </label>
                  <p style={{ 
                    margin: '4px 0 0 0', 
                    fontSize: '14px', 
                    color: '#1f2937',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <Globe size={16} style={{ color: '#6b7280' }} />
                    {leadSourceOptions[contact.lead_source] || contact.lead_source || 'Not specified'}
                  </p>
                </div>
              </div>
            </div>

            {/* Notes */}
            {contact.notes && (
              <div style={{
                padding: '20px',
                backgroundColor: '#fefce8',
                borderRadius: '8px',
                border: '1px solid #fde047'
              }}>
                <h3 style={{
                  margin: '0 0 12px 0',
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#1f2937',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <FileText size={18} />
                  Notes
                </h3>
                <p style={{ 
                  margin: 0, 
                  fontSize: '14px', 
                  color: '#1f2937',
                  lineHeight: '1.6',
                  whiteSpace: 'pre-wrap'
                }}>
                  {contact.notes}
                </p>
              </div>
            )}

            {/* Timestamps */}
            <div style={{
              padding: '20px',
              backgroundColor: '#f1f5f9',
              borderRadius: '8px',
              border: '1px solid #cbd5e1'
            }}>
              <h3 style={{
                margin: '0 0 16px 0',
                fontSize: '16px',
                fontWeight: '600',
                color: '#1f2937',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <Clock size={18} />
                Timeline
              </h3>
              
              <div style={{ display: 'grid', gap: '12px' }}>
                <div>
                  <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                    Created
                  </label>
                  <p style={{ 
                    margin: '4px 0 0 0', 
                    fontSize: '14px', 
                    color: '#1f2937',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <Calendar size={16} style={{ color: '#6b7280' }} />
                    {formatDate(contact.created_at)}
                  </p>
                </div>

                {contact.updated_at && contact.updated_at !== contact.created_at && (
                  <div>
                    <label style={{ fontSize: '12px', fontWeight: '500', color: '#6b7280', textTransform: 'uppercase' }}>
                      Last Updated
                    </label>
                    <p style={{ 
                      margin: '4px 0 0 0', 
                      fontSize: '14px', 
                      color: '#1f2937',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}>
                      <Clock size={16} style={{ color: '#6b7280' }} />
                      {formatDate(contact.updated_at)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: '24px',
            paddingTop: '20px',
            borderTop: '1px solid #e5e7eb'
          }}>
            <button
              onClick={onClose}
              style={{
                padding: '10px 20px',
                border: 'none',
                borderRadius: '6px',
                backgroundColor: '#8b5cf6',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewBusinessContactModal;
