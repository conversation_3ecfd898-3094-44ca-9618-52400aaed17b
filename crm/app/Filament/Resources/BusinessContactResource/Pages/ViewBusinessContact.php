<?php

namespace App\Filament\Resources\BusinessContactResource\Pages;

use App\Filament\Resources\BusinessContactResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBusinessContact extends ViewRecord
{
    protected static string $resource = BusinessContactResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
