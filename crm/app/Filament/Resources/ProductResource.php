<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductImageRelationManagerResource\RelationManagers\ProductImageRelationManager;
use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Filament\Resources\ProductResource\RelationManagers\AccessoriesRelationManager;
use App\Models\Category;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Table;
use Guava\FilamentNestedResources\Ancestor;
use Guava\FilamentNestedResources\Concerns\NestedResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;
use MrBohem\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\ProductImage;
use Storage;

class ProductResource extends Resource
{
    use NestedResource;
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-bookmark';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('user_id')
                    ->label('User')
                    ->searchable()
                    ->getSearchResultsUsing(fn (string $search) => 
                        User::where('name', 'like', "%{$search}%")
                            ->with('profile')
                            ->limit(10)
                            ->get()
                            ->mapWithKeys(fn ($user) => [
                                $user->id => "{$user->name} - " . ($user->profile?->address ?? $user->profile?->phone)
                            ])
                    )
                    ->getOptionLabelUsing(fn ($value) => User::find($value)?->name)
                    ->required(),
                Select::make('category')
                    ->options(Category::all()->pluck('category_name'))
                    ->searchable(),

                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(200),
                Forms\Components\TextInput::make('purchase_cost')
                    ->numeric(),
                Forms\Components\TextInput::make('rental_cost')
                    ->numeric(),
                Forms\Components\TextInput::make('security_deposit_cost')
                    ->numeric(),
                Forms\Components\TextInput::make('rental_days')
                    ->numeric(),
                Forms\Components\TextInput::make('brand_name')
                    ->maxLength(100),
                Forms\Components\TextInput::make('size')
                    ->maxLength(10),
                Forms\Components\TextInput::make('color')
                    ->maxLength(30),
                Forms\Components\TextInput::make('thumbnail')
                    ->maxLength(255),
                Forms\Components\TextInput::make('views')
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('feature')
                    ->maxLength(20),
                Forms\Components\TextInput::make('on_store')
                    ->required()
                    ->numeric()
                    ->default(0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('user_id')
                    ->label('User')
                    ->getStateUsing(function($record){
                        return Storage::disk('s3')->url("Profile/".$record->user_id.".jpg");
                    })
                    ->url(fn ($record) => route('filament.admin.resources.users.view', ['record' => $record->user_id]))
                    ->circular(),
                Tables\Columns\TextColumn::make('category')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                ImageColumn::make('product_image.image_name')
                    ->label('Images')
                    ->getStateUsing(function ($record) {
                        // dump($record->product_image);
                        $images = $record->product_image;


                        if ($images && $images->isNotEmpty()) {
                            $imageUrls = $images->map(function ($image) {
                                return Storage::disk('s3')->url("Product/" . $image->image_name);
                            })->toArray();

                            return $imageUrls;
                        }

                        return []; // Return empty array if no images
                    })
                    ->stacked()
                    ->circular(),
                
                Tables\Columns\TextColumn::make('purchase_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rental_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('security_deposit_cost')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rental_days')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('brand_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('size')
                    ->searchable(),
                Tables\Columns\TextColumn::make('color')
                    ->searchable(),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('views')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('feature')
                    ->searchable(),
                Tables\Columns\TextColumn::make('on_store')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                // Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // ProductImageRelationManager::class,
            // AccessoriesRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            // 'view' => Pages\ViewProduct::route('/{record}'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),

            'accessories' => Pages\ManageProductAccessories::route('/{record}/productAccessories'),
            'productAccessories.create' => Pages\CreateProductAccessories::route('/{record}/productAccessories/create'),

            'images' => Pages\ManageProductImage::route('/{record}/productImage'),
            'product_image.create' => Pages\CreateProductImage::route('/{record}/productImage/create'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditProduct::class,
            Pages\ManageProductAccessories::class,
            Pages\ManageProductImage::class,
        ]);
    }

    public static function getAncestor(): ?Ancestor
    {
        return null;
    }
}
