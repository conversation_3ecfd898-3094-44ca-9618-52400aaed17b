<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductAccessory extends Model
{
    use SoftDeletes,HasUuids;
    protected $fillable = [
        'product_id', 'accessory','price'
    ];

    public function product(){
        return $this->belongsTo(Product::class);
    }

    public function scopeSearch($query, $value)
    {
          if($value == null) return $query;
          $query->Join('users', 'users.id', 'products.user_id')
               ->where(function($q) use($value){
                   $q->orwhereLike('users.name', '%'.$value.'%');
                   $q->orwhereLike('products.name', '%'.$value.'%');
               });
               
          return $query;
    }
}
