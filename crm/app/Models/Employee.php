<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    protected $connection = 'sqlsrv';

    protected $table = 'dbo.Employees';

    public $timestamps = false;

    protected $primaryKey = 'ID';
    protected $fillable = [
        "ID",
        "Title",
        "Full Name",
        "Active",
        "Designation",
        "Date of Joining Primus",
        "On Primus Roll",
        "Date of Relieving from primus",
        "EmployeeGeo",
        "Date of Birth",
        "Gender",
        "Work Email",
        "Billable",
        "Phone Number",
        "Department",
        "Work Experience",
        "Highly Skilled in",
        "Highly Skilled Score",
    ];
}
