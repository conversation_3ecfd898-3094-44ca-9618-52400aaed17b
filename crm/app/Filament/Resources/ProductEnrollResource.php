<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductEnrollResource\Pages;
use App\Filament\Resources\ProductEnrollResource\RelationManagers;
use App\Models\Product;
use App\Models\ProductEnroll;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use MrBohem\LaradrobeServices\Models\ProductAccessory;
use Storage;

class ProductEnrollResource extends Resource
{
    protected static ?string $model = ProductEnroll::class;

    protected static ?string $navigationIcon = 'heroicon-o-chevron-double-down';

    protected static ?string $navigationGroup = 'Enrolls';

    protected static ?string $navigationLabel = 'Product';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            
            Select::make('product_id')
                ->label('User')
                ->searchable()
                ->getSearchResultsUsing(fn (string $search) => 
                    Product::where('name', 'like', "%{$search}%")
                        ->orWhereHas('user', function ($query) use ($search) {
                            $query->where('name', 'like', "%{$search}%");
                        })
                        ->limit(10)
                        ->get()
                        ->mapWithKeys(fn ($product) => [
                            $product->id => "{$product->user->name} - " . ($product->name) 
                        ])
            )
            ->required(),
 
            Grid::make(3) // Create a grid with 3 columns
                ->schema([
                    Forms\Components\Checkbox::make('has_cover'),
                    Forms\Components\Checkbox::make('has_hanger'),
                    Forms\Components\Checkbox::make('is_clean'),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name'),
                ImageColumn::make('product.productImages')
                    ->label('Images')
                    ->getStateUsing(function ($record) {
                        // dump($record->product->product_image);
                        $images = $record->product?->product_image;


                        if ($images && $images->isNotEmpty()) {
                            $imageUrls = $images->map(function ($image) {
                                return Storage::disk('s3')->url("Product/" . $image->image_name);
                            })->toArray();

                            return $imageUrls;
                        }

                        return []; // Return empty array if no images
                    })
                    ->stacked()
                    ->circular(),
                IconColumn::make('has_cover')
                    ->label('Cover')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->trueColor('success')
                    ->falseIcon('heroicon-o-x-circle')
                    ->falseColor('danger'),
                IconColumn::make('has_hanger')
                    ->label('Hanger')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->trueColor('success')
                    ->falseIcon('heroicon-o-x-circle')
                    ->falseColor('danger'),
                IconColumn::make('is_clean')
                    ->label('Clean')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->trueColor('success')
                    ->falseIcon('heroicon-o-x-circle')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                    Action::make('order')
        ->label('Create Order')
        ->icon('heroicon-o-shopping-cart')
        ->form(function ($record) {
            return [
                Forms\Components\Select::make('purpose')
                    ->options([
                        'rent' => 'Rent',
                        'sell' => 'Sell',
                    ])
                    ->required(),
    
                Forms\Components\DatePicker::make('from')
                    ->label('From Date')
                    ->minDate(today())
                    ->required(),
    
                Forms\Components\DatePicker::make('to')
                    ->label('To Date')
                    ->minDate(fn (callable $get) => Carbon::parse($get('from') ?? today())->addDay())
                    ->required(),
    
                Forms\Components\Toggle::make('is_clean')->label('Is Clean'),
                Forms\Components\Toggle::make('has_cover')->label('Has Cover'),
                Forms\Components\Toggle::make('has_hanger')->label('Has Hanger'),
    
                Forms\Components\TextInput::make('security_deposit_cost')
                    ->numeric()
                    ->label('Security Deposit'),
    
                Forms\Components\CheckboxList::make('accessories')
                    ->label('Select Accessories')
                    ->options(
                        ProductAccessory::where('product_id', $record->product_id)
                            ->pluck('accessory', 'id')
                            ->toArray()
                    ),
    
                Forms\Components\Placeholder::make('rental_cost')
                    ->label('Rental Cost')
                    ->content(fn () => '₹ ' . number_format($record->product->rental_cost)),
            ];
        })
        ->action(function ($record, array $data) {
            \App\Models\Order::create([
                'id' => (string) \Illuminate\Support\Str::uuid(),
                'product_id' => $record->id,
                'user_id' => auth()->id(),
                'purpose' => $data['purpose'],
                'from' => $data['from'],
                'to' => $data['to'],
                'is_clean' => $data['is_clean'] ?? false,
                'has_cover' => $data['has_cover'] ?? false,
                'has_hanger' => $data['has_hanger'] ?? false,
                'security_deposit_cost' => $data['security_deposit_cost'],
                'accessories' => $data['accessories'],
                'rental_cost' => $data['rental_cost'],
                'discount_cost' => $data['discount_cost'],
            ]);
        })
        ->modalHeading('Place New Order')
        ->modalSubmitActionLabel('Submit Order'),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductEnrolls::route('/'),
            'create' => Pages\CreateProductEnroll::route('/create'),
            'view' => Pages\ViewProductEnroll::route('/{record}'),
            'edit' => Pages\EditProductEnroll::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
