import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../models/product.dart';
import '../models/user.dart';
import '../models/order.dart';
import '../models/vendor.dart';

class DataProvider with ChangeNotifier {
  // Products
  List<Product> _products = [];
  bool _productsLoading = false;
  String? _productsError;

  // Users
  List<User> _users = [];
  bool _usersLoading = false;
  String? _usersError;

  // Orders
  List<Order> _orders = [];
  bool _ordersLoading = false;
  String? _ordersError;

  // Vendors
  List<Vendor> _vendors = [];
  bool _vendorsLoading = false;
  String? _vendorsError;

  // Dashboard stats
  Map<String, dynamic> _dashboardStats = {};
  bool _statsLoading = false;

  // Getters
  List<Product> get products => _products;
  bool get productsLoading => _productsLoading;
  String? get productsError => _productsError;

  List<User> get users => _users;
  bool get usersLoading => _usersLoading;
  String? get usersError => _usersError;

  List<Order> get orders => _orders;
  bool get ordersLoading => _ordersLoading;
  String? get ordersError => _ordersError;

  List<Vendor> get vendors => _vendors;
  bool get vendorsLoading => _vendorsLoading;
  String? get vendorsError => _vendorsError;

  Map<String, dynamic> get dashboardStats => _dashboardStats;
  bool get statsLoading => _statsLoading;

  // Products methods
  Future<void> fetchProducts({Map<String, dynamic>? filters}) async {
    _productsLoading = true;
    _productsError = null;
    notifyListeners();

    try {
      final response = await ApiService.getProducts(filters: filters);
      if (response['success'] == true || response['data'] != null) {
        // Success response - check if it has data structure or direct array
        final productsData = response['data'] ?? response;
        final productsList = productsData is Map ? productsData['data'] : productsData;

        if (productsList is List) {
          _products = productsList.map((json) => Product.fromMap(json)).toList();
        } else {
          _productsError = 'Invalid products data format';
        }
      } else {
        _productsError = response['message'] ?? 'Failed to fetch products';
      }
    } catch (e) {
      _productsError = 'Error fetching products: ${e.toString()}';
    }

    _productsLoading = false;
    notifyListeners();
  }

  Future<bool> createProduct(Map<String, dynamic> productData) async {
    try {
      final response = await ApiService.createProduct(productData);
      if (response['message'] == "Product created successfully") {
        await fetchProducts(); // Refresh list
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error creating product: $e');
      return false;
    }
  }

  Future<bool> updateProduct(String id, Map<String, dynamic> productData) async {
    try {
      final response = await ApiService.updateProduct(id, productData);
      if (response['message'] == "Product updated successfully") {
        await fetchProducts(); // Refresh list
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating product: $e');
      return false;
    }
  }

  Future<bool> deleteProduct(String id) async {
    try {
      final response = await ApiService.deleteProduct(id);
      if (response['success'] == true || response['message'] == null) {
        await fetchProducts(); // Refresh list
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting product: $e');
      return false;
    }
  }

  // Users methods
  Future<void> fetchUsers({Map<String, dynamic>? filters}) async {
    _usersLoading = true;
    _usersError = null;
    notifyListeners();

    try {
      final response = await ApiService.getUsers(filters: filters);
      if (response['success'] == true || response['data'] != null) {
        // Success response - check if it has data structure or direct array
        final usersData = response['data'] ?? response;
        final usersList = usersData is Map ? usersData['data'] : usersData;

        if (usersList is List) {
          _users = usersList.map((json) => User.fromMap(json)).toList();
        } else {
          _usersError = 'Invalid users data format';
        }
      } else {
        _usersError = response['message'] ?? 'Failed to fetch users';
      }
    } catch (e) {
      _usersError = 'Error fetching users: ${e.toString()}';
    }

    _usersLoading = false;
    notifyListeners();
  }

  // Orders methods
  Future<void> fetchOrders({Map<String, dynamic>? filters}) async {
    _ordersLoading = true;
    _ordersError = null;
    notifyListeners();

    try {
      final response = await ApiService.getOrders(filters: filters);
      if (response['success'] == true) {
        _orders = (response['data']['data'] as List)
            .map((json) => Order.fromMap(json))
            .toList();
      } else {
        _ordersError = response['message'] ?? 'Failed to fetch orders';
      }
    } catch (e) {
      _ordersError = 'Error fetching orders: ${e.toString()}';
    }

    _ordersLoading = false;
    notifyListeners();
  }

  // Vendors methods
  Future<void> fetchVendors({Map<String, dynamic>? filters}) async {
    _vendorsLoading = true;
    _vendorsError = null;
    notifyListeners();

    try {
      final response = await ApiService.getVendors(filters: filters);
      if (response['vendors'] != null) {
        // Vendors API returns {vendors: [...]} format
        _vendors = (response['vendors'] as List)
            .map((json) => Vendor.fromMap(json))
            .toList();
      } else if (response['message'] != null) {
        _vendorsError = response['message'];
      } else {
        _vendorsError = 'Failed to fetch vendors';
      }
    } catch (e) {
      _vendorsError = 'Error fetching vendors: ${e.toString()}';
    }

    _vendorsLoading = false;
    notifyListeners();
  }

  // Dashboard methods
  Future<void> fetchDashboardStats() async {
    _statsLoading = true;
    notifyListeners();

    try {
      final response = await ApiService.getDashboardStats();
      if (response['success'] == true) {
        _dashboardStats = response['data'];
      }
    } catch (e) {
      debugPrint('Error fetching dashboard stats: $e');
    }

    _statsLoading = false;
    notifyListeners();
  }

  void clearErrors() {
    _productsError = null;
    _usersError = null;
    _ordersError = null;
    _vendorsError = null;
    notifyListeners();
  }
}
