<?php

namespace App\Filament\Resources\ProductAccessoriesImageResource\Pages;

use App\Filament\Resources\ProductAccessoriesImageResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Guava\FilamentNestedResources\Concerns\NestedPage;

class CreateProductAccessoriesImage extends CreateRecord
{
    use NestedPage;
    protected static string $resource = ProductAccessoriesImageResource::class;
}
