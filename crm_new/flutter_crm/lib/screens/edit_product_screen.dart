import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/data_provider.dart';
import '../models/product.dart';
import '../widgets/app_layout.dart';
import '../utils/app_theme.dart';

class EditProductScreen extends StatefulWidget {
  final Product product;

  const EditProductScreen({super.key, required this.product});

  @override
  State<EditProductScreen> createState() => _EditProductScreenState();
}

class _EditProductScreenState extends State<EditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _categoryController = TextEditingController();
  final _purchaseCostController = TextEditingController();
  final _rentalCostController = TextEditingController();
  final _securityDepositController = TextEditingController();
  final _rentalDaysController = TextEditingController();
  final _brandNameController = TextEditingController();
  final _sizeController = TextEditingController();
  final _colorController = TextEditingController();
  final _featureController = TextEditingController();
  
  bool _onStore = true;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final product = widget.product;
    _nameController.text = product.name;
    _categoryController.text = product.category ?? '';
    _purchaseCostController.text = product.purchaseCost?.toString() ?? '';
    _rentalCostController.text = product.rentalCost.toString();
    _securityDepositController.text = product.securityDepositCost?.toString() ?? '';
    _rentalDaysController.text = product.rentalDays?.toString() ?? '';
    _brandNameController.text = product.brandName ?? '';
    _sizeController.text = product.size ?? '';
    _colorController.text = product.color ?? '';
    _featureController.text = product.feature ?? '';
    _onStore = product.isAvailableOnStore;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _categoryController.dispose();
    _purchaseCostController.dispose();
    _rentalCostController.dispose();
    _securityDepositController.dispose();
    _rentalDaysController.dispose();
    _brandNameController.dispose();
    _sizeController.dispose();
    _colorController.dispose();
    _featureController.dispose();
    super.dispose();
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final dataProvider = Provider.of<DataProvider>(context, listen: false);
      
      final productData = {
        'name': _nameController.text.trim(),
        'category': _categoryController.text.trim(),
        'purchase_cost': _purchaseCostController.text.isNotEmpty 
            ? double.parse(_purchaseCostController.text) 
            : null,
        'rental_cost': double.parse(_rentalCostController.text),
        'security_deposit_cost': _securityDepositController.text.isNotEmpty 
            ? double.parse(_securityDepositController.text) 
            : null,
        'rental_days': _rentalDaysController.text.isNotEmpty 
            ? double.parse(_rentalDaysController.text) 
            : null,
        'brand_name': _brandNameController.text.trim().isNotEmpty 
            ? _brandNameController.text.trim() 
            : null,
        'size': _sizeController.text.trim().isNotEmpty 
            ? _sizeController.text.trim() 
            : null,
        'color': _colorController.text.trim().isNotEmpty 
            ? _colorController.text.trim() 
            : null,
        'feature': _featureController.text.trim().isNotEmpty 
            ? _featureController.text.trim() 
            : null,
        'on_store': _onStore ? 1 : 0,
      };

      final success = await dataProvider.updateProduct(widget.product.id, productData);
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Product updated successfully!'),
            backgroundColor: AppTheme.success,
          ),
        );
        Navigator.of(context).pop(true);
      } else {
        setState(() {
          _error = 'Failed to update product. Please try again.';
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppLayout(
      title: 'Edit Product',
      currentRoute: '/edit-product',
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.warning,
                      AppTheme.warning.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.edit,
                      size: 32,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Edit Product',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Editing: ${widget.product.name}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _updateProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppTheme.warning,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Update Product'),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              if (_error != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: AppTheme.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: AppTheme.error.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: AppTheme.error),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _error!,
                          style: TextStyle(color: AppTheme.error),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Basic Information Section
              _buildSection(
                title: 'Basic Information',
                icon: Icons.info_outline,
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'Product Name *',
                    icon: Icons.inventory_2,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Product name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _categoryController,
                    label: 'Category *',
                    icon: Icons.category,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Category is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _brandNameController,
                    label: 'Brand Name',
                    icon: Icons.branding_watermark,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Pricing Section
              _buildSection(
                title: 'Pricing Information',
                icon: Icons.currency_rupee,
                children: [
                  _buildTextField(
                    controller: _rentalCostController,
                    label: 'Rental Cost *',
                    icon: Icons.currency_rupee,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Rental cost is required';
                      }
                      final cost = double.tryParse(value);
                      if (cost == null || cost <= 0) {
                        return 'Rental cost must be greater than 0';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _purchaseCostController,
                          label: 'Purchase Cost',
                          icon: Icons.shopping_cart,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              final cost = double.tryParse(value);
                              if (cost == null || cost < 0) {
                                return 'Purchase cost cannot be negative';
                              }
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildTextField(
                          controller: _securityDepositController,
                          label: 'Security Deposit',
                          icon: Icons.security,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              final cost = double.tryParse(value);
                              if (cost == null || cost < 0) {
                                return 'Security deposit cannot be negative';
                              }
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _rentalDaysController,
                    label: 'Rental Days',
                    icon: Icons.calendar_today,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final days = int.tryParse(value);
                        if (days == null || days < 1 || days > 365) {
                          return 'Rental days must be between 1 and 365';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Product Details Section
              _buildSection(
                title: 'Product Details',
                icon: Icons.details,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _sizeController,
                          label: 'Size',
                          icon: Icons.straighten,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildTextField(
                          controller: _colorController,
                          label: 'Color',
                          icon: Icons.palette,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _featureController,
                    label: 'Feature',
                    icon: Icons.star,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Availability Section
              _buildSection(
                title: 'Availability',
                icon: Icons.store,
                children: [
                  SwitchListTile(
                    title: const Text('Available in Store'),
                    subtitle: const Text('Is this product available for rent?'),
                    value: _onStore,
                    onChanged: (value) {
                      setState(() {
                        _onStore = value;
                      });
                    },
                    activeColor: AppTheme.primary,
                  ),
                ],
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.gray900,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      keyboardType: keyboardType,
      validator: validator,
    );
  }
}
