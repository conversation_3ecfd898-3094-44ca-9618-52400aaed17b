<?php

namespace App\Models;

use App\Enums\ReferralStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Referral extends Model
{
    use HasFactory,SoftDeletes,HasUuids;

    protected $fillable = [
        'referrer_id',
        'referee_id',
        'status',
        'points',
    ];

    protected $casts = [
        'status' => ReferralStatus::class
    ];

    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    public function referee()
    {
        return $this->belongsTo(User::class, 'referee_id');
    }

    public static function getPoints($status){
        return match ($status) {
            ReferralStatus::Pending => 0,
            ReferralStatus::Accepted => 10,
            ReferralStatus::Rejected => 0,
        };
    }
}
