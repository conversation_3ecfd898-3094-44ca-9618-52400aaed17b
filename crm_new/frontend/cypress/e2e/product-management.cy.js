describe('Product Management', () => {
  beforeEach(() => {
    // Login before each test
    cy.visit('/login');
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password');
    cy.get('[data-testid="login-button"]').click();
    
    // Wait for redirect to dashboard
    cy.url().should('include', '/dashboard');
    
    // Navigate to products page
    cy.visit('/products');
  });

  it('should display products list', () => {
    cy.get('[data-testid="products-header"]').should('contain', 'Products');
    cy.get('[data-testid="products-table"]').should('be.visible');
    cy.get('[data-testid="product-row"]').should('have.length.at.least', 1);
  });

  it('should search products by name', () => {
    cy.get('[data-testid="search-input"]').type('Pink Reception');
    cy.get('[data-testid="product-row"]').should('contain', 'Pink Reception');
    
    // Clear search
    cy.get('[data-testid="search-input"]').clear();
    cy.get('[data-testid="product-row"]').should('have.length.at.least', 1);
  });

  it('should filter products by status', () => {
    cy.get('[data-testid="status-filter"]').select('needs_cleaning');
    cy.get('[data-testid="product-row"]').each(($row) => {
      cy.wrap($row).find('[data-testid="status-badge"]').should('contain', 'Needs Cleaning');
    });
    
    // Reset filter
    cy.get('[data-testid="status-filter"]').select('all');
  });

  it('should update product maintenance status', () => {
    // Find a product that's ready and click maintenance button
    cy.get('[data-testid="product-row"]')
      .contains('Ready')
      .closest('[data-testid="product-row"]')
      .find('[data-testid="maintenance-button"]')
      .click();

    // Modal should open
    cy.get('[data-testid="maintenance-modal"]').should('be.visible');
    cy.get('[data-testid="modal-title"]').should('contain', 'Update Maintenance Status');

    // Mark as dirty
    cy.get('[data-testid="mark-dirty-button"]').click();

    // Modal should close and status should update
    cy.get('[data-testid="maintenance-modal"]').should('not.exist');
    
    // Verify status changed (this might need a page refresh or real-time update)
    cy.reload();
    cy.get('[data-testid="status-badge"]').should('contain', 'Needs Cleaning');
  });

  it('should refresh products list', () => {
    cy.get('[data-testid="refresh-button"]').click();
    cy.get('[data-testid="loading-indicator"]').should('be.visible');
    cy.get('[data-testid="loading-indicator"]').should('not.exist');
    cy.get('[data-testid="product-row"]').should('have.length.at.least', 1);
  });

  it('should display product details correctly', () => {
    cy.get('[data-testid="product-row"]').first().within(() => {
      cy.get('[data-testid="product-name"]').should('not.be.empty');
      cy.get('[data-testid="product-description"]').should('not.be.empty');
      cy.get('[data-testid="rental-cost"]').should('contain', '₹');
      cy.get('[data-testid="security-deposit"]').should('contain', '₹');
      cy.get('[data-testid="status-badge"]').should('be.visible');
    });
  });

  it('should show vendor information for assigned products', () => {
    // Look for products with vendor assignments
    cy.get('[data-testid="product-row"]').each(($row) => {
      cy.wrap($row).then(($el) => {
        if ($el.find('[data-testid="vendor-info"]').length > 0) {
          cy.wrap($el).find('[data-testid="vendor-info"]').should('contain', 'With:');
        }
      });
    });
  });

  it('should disable maintenance button for products with vendors', () => {
    // Find products that are with vendors
    cy.get('[data-testid="product-row"]').each(($row) => {
      cy.wrap($row).then(($el) => {
        if ($el.find('[data-testid="vendor-info"]').length > 0) {
          cy.wrap($el).find('[data-testid="maintenance-button"]').should('be.disabled');
        }
      });
    });
  });

  it('should handle empty search results', () => {
    cy.get('[data-testid="search-input"]').type('NonexistentProduct123');
    cy.get('[data-testid="no-products-message"]').should('contain', 'No products found');
    
    // Clear search to restore products
    cy.get('[data-testid="search-input"]').clear();
    cy.get('[data-testid="product-row"]').should('have.length.at.least', 1);
  });

  it('should display correct product count', () => {
    cy.get('[data-testid="product-count"]').should('contain', 'products');
    cy.get('[data-testid="product-count"]').invoke('text').then((text) => {
      const count = parseInt(text.match(/\d+/)[0]);
      cy.get('[data-testid="product-row"]').should('have.length', count);
    });
  });

  it('should maintain status colors consistency', () => {
    const statusColors = {
      'Ready': 'green',
      'Needs Cleaning': 'orange',
      'Damaged': 'red',
      'Under Cleaning': 'blue',
      'Under Repair': 'blue',
      'Under Maintenance': 'blue'
    };

    Object.entries(statusColors).forEach(([status, color]) => {
      cy.get('[data-testid="product-row"]').each(($row) => {
        cy.wrap($row).then(($el) => {
          const statusBadge = $el.find('[data-testid="status-badge"]');
          if (statusBadge.text().includes(status)) {
            // Check if the badge has the correct color class or style
            cy.wrap(statusBadge).should('have.class', `status-${color}`);
          }
        });
      });
    });
  });
});
