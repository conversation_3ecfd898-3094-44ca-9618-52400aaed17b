<?php

namespace App\Filament\Resources\ProductAccessoriesImageResource\Pages;

use App\Filament\Resources\ProductAccessoriesImageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Guava\FilamentNestedResources\Concerns\NestedPage;

class ListProductAccessoriesImages extends ListRecords
{
    use NestedPage;
    protected static string $resource = ProductAccessoriesImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
