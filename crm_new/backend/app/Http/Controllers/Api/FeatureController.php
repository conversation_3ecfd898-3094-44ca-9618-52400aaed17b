<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\FeatureServices;
use Mr<PERSON><PERSON>em\LaradrobeServices\SizeServices;
use Illuminate\Http\Request;

class FeatureController extends Controller
{
    /**
     * Get all available sizes from SizeServices
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSizes()
    {
        try {
            $sizes = SizeServices::$size;

            return response()->json([
                'success' => true,
                'data' => $sizes,
                'message' => 'Sizes retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve sizes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all available features from FeatureServices
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFeatures()
    {
        try {
            $features = FeatureServices::$feature;

            return response()->json([
                'success' => true,
                'data' => $features,
                'message' => 'Features retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve features',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
