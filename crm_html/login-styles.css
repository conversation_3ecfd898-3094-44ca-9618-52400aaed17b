/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Login Container */
.login-container {
    display: flex;
    min-height: 100vh;
}

/* Left Side - Branding */
.login-branding {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 64px;
    overflow: hidden;
}

.branding-content {
    position: relative;
    z-index: 10;
    text-align: center;
    color: white;
    max-width: 500px;
}

.logo-section {
    margin-bottom: 64px;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 32px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-section h1 {
    font-size: 48px;
    font-weight: 900;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-section p {
    font-size: 20px;
    opacity: 0.9;
    font-weight: 400;
}

/* Stats Showcase */
.stats-showcase {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
    margin-bottom: 64px;
}

.stat-item {
    text-align: center;
    padding: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    font-size: 36px;
    font-weight: 900;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 16px;
    opacity: 0.8;
    font-weight: 500;
}

/* Testimonial */
.testimonial {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 32px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.testimonial-content {
    position: relative;
}

.testimonial-content .fa-quote-left {
    font-size: 24px;
    opacity: 0.6;
    margin-bottom: 16px;
}

.testimonial-content p {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 24px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
}

.author-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-name {
    font-weight: 600;
    font-size: 16px;
}

.author-title {
    font-size: 14px;
    opacity: 0.8;
}

/* Decorative Elements */
.decoration {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.decoration-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.decoration-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 10%;
    animation-delay: 2s;
}

.decoration-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Right Side - Login Form */
.login-form-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background: #f8fafc;
}

.form-container {
    width: 100%;
    max-width: 480px;
}

/* Mobile Logo */
.mobile-logo {
    display: none;
    text-align: center;
    margin-bottom: 32px;
}

.mobile-logo .logo-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    color: white;
}

.mobile-logo h2 {
    font-size: 28px;
    font-weight: 800;
    color: #1e293b;
}

/* Login Form Card */
.login-form-card {
    background: white;
    border-radius: 24px;
    padding: 48px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-header h3 {
    font-size: 32px;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 12px;
}

.form-header p {
    font-size: 18px;
    color: #64748b;
}

/* Error Message */
.error-message {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #fee2e2;
    color: #991b1b;
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 24px;
    border: 1px solid #fecaca;
}

.error-message i {
    font-size: 16px;
}

/* Form Styles */
.login-form {
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.input-wrapper {
    position: relative;
}

.input-wrapper i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 16px;
}

.input-wrapper input {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #6b7280;
}

/* Form Options */
.form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: #3b82f6;
    border-color: #3b82f6;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.forgot-password {
    color: #3b82f6;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #1d4ed8;
}

/* Login Button */
.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 18px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Demo Credentials */
.demo-credentials {
    background: #eff6ff;
    border: 1px solid #dbeafe;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.demo-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #1d4ed8;
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 14px;
}

.demo-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.demo-item {
    font-size: 14px;
    color: #1e40af;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.demo-item:hover {
    background: #dbeafe;
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 32px;
}

.form-footer p {
    color: #9ca3af;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .login-branding {
        padding: 32px;
    }
    
    .stats-showcase {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .stat-item {
        padding: 16px;
    }
    
    .stat-number {
        font-size: 28px;
    }
    
    .testimonial {
        padding: 24px;
    }
}

@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }
    
    .login-branding {
        display: none;
    }
    
    .mobile-logo {
        display: block;
    }
    
    .login-form-section {
        padding: 16px;
    }
    
    .login-form-card {
        padding: 32px 24px;
    }
    
    .form-header h3 {
        font-size: 28px;
    }
    
    .form-header p {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .login-form-card {
        padding: 24px 16px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
}
