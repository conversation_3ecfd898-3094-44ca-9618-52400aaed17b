<?php

namespace App\Filament\Resources\ProductAccessoriesResource\Pages;

use App\Filament\Resources\ProductAccessoriesResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Guava\FilamentNestedResources\Concerns\NestedPage;

class CreateProductAccessories extends CreateRecord

{
    use NestedPage;

    protected static string $resource = ProductAccessoriesResource::class;
}
