<?php

namespace App\Filament\Resources\MasterCityResource\Pages;

use App\Filament\Resources\MasterCityResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMasterCity extends EditRecord
{
    protected static string $resource = MasterCityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}
