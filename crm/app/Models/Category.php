<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Genral\Shop;
use App\Models\Master\MstCategory;

class Category extends Model
{
    use SoftDeletes;
    
    protected $guarded = [];

    protected $date = ['deleted_at'];
    
    public function mstCategory()
    {
		return $this->belongsTo(MstCategory::class, 'mst_category','mst_category_name');
    }
    
    public function user()
    {
    	return $this->belongsTo(User::class);
    }

    public function shop(){
        return $this->belongsTo(Shop::class);
    }

    public function product(){
        return $this->hasMany(Product::class, 'product_category','mst_category');
    }

	public function parent()
	{
		return $this->belongsTo(self::class,'parent_id');
	}

	public function children()
	{
		return $this->hasMany(self::class,'parent_id');
	}

    //public function productDetail()
    //{
    //    return $this->hasManyThrough(ProductDetail::class,Product::class);
    //}
    
	public static function showProductCategoryTree($id)
	{
		$productCategory = ProductCategory::with('mstCategory')
										// ->where('user_id', $id)
										->where('shop_id',$id)
										->get();

		foreach ($productCategory as $index => $row) {
			$sub_data["id"] = $row["id"];
			$sub_data["title"] = $row["mstCategory"]["mst_category_name"];
			$sub_data["parent_id"] = $row["parent_id"];
			$sub_data["slug"] = $row["mstCategory"]["slug"];
			$data[] = $sub_data;
		}
		foreach ($data as $key => &$value) {
			$output[$value["id"]] = &$value;
		}
		foreach ($data as $key => &$value) {
			if ($value["parent_id"] && isset($output[$value["parent_id"]])) {
				$output[$value["parent_id"]]["nodes"][] = &$value;
			}
		}
		foreach ($data as $key => &$value) {
			if ($value["parent_id"] && isset($output[$value["parent_id"]])) {
				unset($data[$key]);
			}
		}

//		$new = json_encode($data);

		return $data;
	}

    public static function getProductsByShop($user_id){
        return ProductCategory::with(['product','productDetail'])
                            ->where('user_id',$user_id)
                            ->first();
    }
    
	public static function getProductsByCategoryId($categoryId)
	{
		return self::with(['product','product.product_image' => function($query){
						$query->where('thumbnail',1)->first();
					}, 'product.mst_product_image'])
						->find($categoryId);
	}

	public static function getProductsByCategoryIdWithImageLimit($categoryId)
	{
		return self::with(['product', 'product.product_image' => function ($query) {
			$query->where('thumbnail', 1)->first();
		}, 
		'product.mst_product_image'=>function($query){
			$query->limit(2);
		}
		])
		->find($categoryId);
	}

    
}
