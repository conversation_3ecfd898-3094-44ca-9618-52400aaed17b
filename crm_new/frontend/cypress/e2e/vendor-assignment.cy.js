describe('Vendor Assignment Workflow', () => {
  beforeEach(() => {
    // Login
    cy.visit('/login');
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password');
    cy.get('[data-testid="login-button"]').click();
    cy.url().should('include', '/dashboard');
  });

  it('should complete full vendor assignment workflow', () => {
    // Step 1: Mark a product as dirty
    cy.visit('/products');
    
    // Find a ready product and mark as dirty
    cy.get('[data-testid="product-row"]')
      .contains('Ready')
      .closest('[data-testid="product-row"]')
      .within(() => {
        cy.get('[data-testid="product-name"]').invoke('text').as('productName');
        cy.get('[data-testid="maintenance-button"]').click();
      });

    cy.get('[data-testid="maintenance-modal"]').should('be.visible');
    cy.get('[data-testid="mark-dirty-button"]').click();
    cy.get('[data-testid="maintenance-modal"]').should('not.exist');

    // Step 2: Go to vendors page and assign product
    cy.visit('/vendors');
    cy.get('[data-testid="vendors-header"]').should('contain', 'Vendors');

    // Find a dry cleaner and assign product
    cy.get('[data-testid="vendor-card"]')
      .contains('Dry Cleaner')
      .closest('[data-testid="vendor-card"]')
      .within(() => {
        cy.get('[data-testid="assign-product-button"]').click();
      });

    // Product selection modal should open
    cy.get('[data-testid="product-selection-modal"]').should('be.visible');
    cy.get('[data-testid="modal-title"]').should('contain', 'Assign Product');

    // Select the product we marked as dirty
    cy.get('@productName').then((productName) => {
      cy.get('[data-testid="product-item"]')
        .contains(productName)
        .closest('[data-testid="product-item"]')
        .find('[data-testid="assign-button"]')
        .click();
    });

    // Assignment should succeed
    cy.get('[data-testid="product-selection-modal"]').should('not.exist');
    
    // Step 3: Verify product appears in "Products with Vendors" section
    cy.get('[data-testid="products-with-vendors"]').should('be.visible');
    cy.get('@productName').then((productName) => {
      cy.get('[data-testid="assignment-card"]').should('contain', productName);
    });

    // Step 4: Verify product status changed to "Under Cleaning"
    cy.visit('/products');
    cy.get('@productName').then((productName) => {
      cy.get('[data-testid="product-row"]')
        .contains(productName)
        .closest('[data-testid="product-row"]')
        .find('[data-testid="status-badge"]')
        .should('contain', 'Under Cleaning');
    });
  });

  it('should prevent assigning already assigned products', () => {
    cy.visit('/vendors');

    // Try to assign a product that's already with a vendor
    cy.get('[data-testid="vendor-card"]')
      .first()
      .find('[data-testid="assign-product-button"]')
      .click();

    cy.get('[data-testid="product-selection-modal"]').should('be.visible');

    // Should not show products that are already assigned
    cy.get('[data-testid="product-item"]').each(($item) => {
      cy.wrap($item).find('[data-testid="status-badge"]').should('not.contain', 'Under Cleaning');
      cy.wrap($item).find('[data-testid="status-badge"]').should('not.contain', 'Under Repair');
    });
  });

  it('should return product from vendor', () => {
    cy.visit('/vendors');

    // Find a product that's with a vendor
    cy.get('[data-testid="assignment-card"]').first().within(() => {
      cy.get('[data-testid="product-name"]').invoke('text').as('productName');
      cy.get('[data-testid="return-button"]').click();
    });

    // Return confirmation modal should open
    cy.get('[data-testid="return-modal"]').should('be.visible');
    cy.get('[data-testid="confirm-return-button"]').click();

    // Modal should close
    cy.get('[data-testid="return-modal"]').should('not.exist');

    // Product should be removed from assignments list or status updated
    cy.get('@productName').then((productName) => {
      // Either the assignment card is gone or status shows returned
      cy.get('body').then(($body) => {
        if ($body.find(`[data-testid="assignment-card"]:contains("${productName}")`).length > 0) {
          cy.get('[data-testid="assignment-card"]')
            .contains(productName)
            .closest('[data-testid="assignment-card"]')
            .should('contain', 'returned');
        }
      });
    });

    // Verify product status changed back to "Ready" in products page
    cy.visit('/products');
    cy.get('@productName').then((productName) => {
      cy.get('[data-testid="product-row"]')
        .contains(productName)
        .closest('[data-testid="product-row"]')
        .find('[data-testid="status-badge"]')
        .should('contain', 'Ready');
    });
  });

  it('should filter assignment modal by vendor type', () => {
    cy.visit('/vendors');

    // Test dry cleaner - should only show products needing cleaning
    cy.get('[data-testid="vendor-card"]')
      .contains('Dry Cleaner')
      .closest('[data-testid="vendor-card"]')
      .find('[data-testid="assign-product-button"]')
      .click();

    cy.get('[data-testid="product-selection-modal"]').should('be.visible');
    cy.get('[data-testid="product-item"]').each(($item) => {
      cy.wrap($item).find('[data-testid="status-badge"]').then(($badge) => {
        const status = $badge.text();
        expect(['Needs Cleaning', 'Damaged']).to.include(status);
      });
    });

    cy.get('[data-testid="close-modal-button"]').click();

    // Test repair shop - should show damaged products
    cy.get('[data-testid="vendor-card"]')
      .contains('Repair')
      .closest('[data-testid="vendor-card"]')
      .find('[data-testid="assign-product-button"]')
      .click();

    cy.get('[data-testid="product-selection-modal"]').should('be.visible');
    // Should show products that need repair/maintenance
    cy.get('[data-testid="product-item"]').should('have.length.at.least', 0);
  });

  it('should search products in assignment modal', () => {
    cy.visit('/vendors');

    cy.get('[data-testid="vendor-card"]')
      .first()
      .find('[data-testid="assign-product-button"]')
      .click();

    cy.get('[data-testid="product-selection-modal"]').should('be.visible');
    
    // Search for a specific product
    cy.get('[data-testid="search-input"]').type('Pink');
    cy.get('[data-testid="product-item"]').each(($item) => {
      cy.wrap($item).find('[data-testid="product-name"]').should('contain', 'Pink');
    });

    // Clear search
    cy.get('[data-testid="search-input"]').clear();
    cy.get('[data-testid="product-item"]').should('have.length.at.least', 1);
  });

  it('should show vendor assignment counts', () => {
    cy.visit('/vendors');

    cy.get('[data-testid="vendor-card"]').each(($card) => {
      cy.wrap($card).find('[data-testid="assignment-count"]').should('be.visible');
      cy.wrap($card).find('[data-testid="assignment-count"]').should('contain', 'assignment');
    });
  });

  it('should handle no products available for assignment', () => {
    // This test assumes all products are either clean or already assigned
    cy.visit('/vendors');

    cy.get('[data-testid="vendor-card"]')
      .first()
      .find('[data-testid="assign-product-button"]')
      .click();

    cy.get('[data-testid="product-selection-modal"]').should('be.visible');
    
    // If no products available, should show appropriate message
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="product-item"]').length === 0) {
        cy.get('[data-testid="no-products-message"]').should('contain', 'No products available');
      }
    });
  });

  it('should display assignment details correctly', () => {
    cy.visit('/vendors');

    cy.get('[data-testid="assignment-card"]').first().within(() => {
      cy.get('[data-testid="product-name"]').should('not.be.empty');
      cy.get('[data-testid="vendor-name"]').should('not.be.empty');
      cy.get('[data-testid="assignment-type"]').should('not.be.empty');
      cy.get('[data-testid="assigned-date"]').should('not.be.empty');
      cy.get('[data-testid="status-badge"]').should('be.visible');
    });
  });
});
