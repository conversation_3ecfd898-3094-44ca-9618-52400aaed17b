<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\ColorServices;
use Illuminate\Http\Request;

class ColorController extends Controller
{
    /**
     * Get all available colors
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $colors = ColorServices::$color;
            
            return response()->json([
                'success' => true,
                'data' => $colors,
                'message' => 'Colors retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve colors',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
