<?php

namespace Tests\Unit\Services;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Services\VendorService;
use App\Vendor;
use App\ProductVendorAssignment;
use Mr<PERSON><PERSON>em\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\ProductMaintenanceService;
use App\User;

class VendorServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $vendor;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->vendor = Vendor::factory()->create(['type' => 'dry_cleaner']);
        $this->product = Product::factory()->create();
        
        // Authenticate user
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_assign_product_to_vendor()
    {
        // Mark product as dirty first
        ProductMaintenanceService::updateStatusManually($this->product->id, 'dirty', 'Test', $this->user->id);

        $assignmentData = [
            'assignment_type' => 'cleaning',
            'expected_return_date' => now()->addDays(3)->toDateString(),
            'cost' => 50.00,
            'work_description' => 'Deep cleaning required',
            'internal_notes' => 'Handle with care'
        ];

        $assignment = VendorService::assignProductToVendor(
            $this->product->id,
            $this->vendor->id,
            $assignmentData
        );

        $this->assertInstanceOf(ProductVendorAssignment::class, $assignment);
        $this->assertEquals($this->product->id, $assignment->product_id);
        $this->assertEquals($this->vendor->id, $assignment->vendor_id);
        $this->assertEquals('cleaning', $assignment->assignment_type);
        $this->assertEquals('assigned', $assignment->status);

        // Check product status was updated
        $status = ProductMaintenanceService::getCurrentStatus($this->product->id);
        $this->assertEquals('under_cleaning', $status['maintenance_status']);
    }

    /** @test */
    public function it_prevents_assigning_already_assigned_product()
    {
        // Create existing assignment
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Product is already assigned to a vendor');

        VendorService::assignProductToVendor(
            $this->product->id,
            $this->vendor->id,
            ['assignment_type' => 'repair']
        );
    }

    /** @test */
    public function it_can_mark_product_as_returned()
    {
        // Create assignment
        $assignment = ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $returnData = [
            'actual_return_date' => now()->toDateString(),
            'vendor_notes' => 'Cleaning completed successfully',
            'cost' => 45.00
        ];

        $returnedAssignment = VendorService::markProductAsReturned($assignment->id, $returnData);

        $this->assertEquals('returned', $returnedAssignment->status);
        $this->assertEquals('Cleaning completed successfully', $returnedAssignment->vendor_notes);
        $this->assertEquals(45.00, $returnedAssignment->cost);

        // Check product status was updated
        $status = ProductMaintenanceService::getCurrentStatus($this->product->id);
        $this->assertEquals('ready', $status['maintenance_status']);
    }

    /** @test */
    public function it_determines_correct_status_after_return()
    {
        $cleaningAssignment = ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        VendorService::markProductAsReturned($cleaningAssignment->id, []);

        $status = ProductMaintenanceService::getCurrentStatus($this->product->id);
        $this->assertEquals('ready', $status['maintenance_status']);
    }

    /** @test */
    public function it_can_get_products_with_vendors()
    {
        // Create multiple assignments
        $product2 = Product::factory()->create();
        
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        ProductVendorAssignment::create([
            'product_id' => $product2->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'repair',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        // Create returned assignment (should not be included)
        ProductVendorAssignment::create([
            'product_id' => Product::factory()->create()->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'returned',
            'assigned_date' => now()->subDays(2)->toDateString(),
            'actual_return_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $productsWithVendors = VendorService::getProductsWithVendors();

        $this->assertCount(2, $productsWithVendors);
        
        $productIds = $productsWithVendors->pluck('product_id')->toArray();
        $this->assertContains($this->product->id, $productIds);
        $this->assertContains($product2->id, $productIds);
    }

    /** @test */
    public function it_can_check_if_product_is_with_vendor()
    {
        // Product not with vendor
        $result = VendorService::isProductWithVendor($this->product->id);
        $this->assertFalse($result['is_with_vendor']);

        // Assign product to vendor
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $result = VendorService::isProductWithVendor($this->product->id);
        $this->assertTrue($result['is_with_vendor']);
        $this->assertEquals($this->vendor->name, $result['vendor_name']);
        $this->assertEquals('cleaning', $result['assignment_type']);
    }

    /** @test */
    public function it_can_get_vendor_dashboard_data()
    {
        // Create assignments for vendor
        ProductVendorAssignment::factory()->count(3)->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'assigned'
        ]);

        ProductVendorAssignment::factory()->count(2)->create([
            'vendor_id' => $this->vendor->id,
            'status' => 'returned'
        ]);

        // Create overdue assignment
        ProductVendorAssignment::create([
            'product_id' => Product::factory()->create()->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->subDays(5)->toDateString(),
            'expected_return_date' => now()->subDays(1)->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $dashboard = VendorService::getVendorDashboard($this->vendor->id);

        $this->assertEquals(6, $dashboard['total_assignments']);
        $this->assertEquals(4, $dashboard['active_assignments']); // 3 + 1 overdue
        $this->assertEquals(2, $dashboard['completed_assignments']);
        $this->assertEquals(1, $dashboard['overdue_assignments']);
    }

    /** @test */
    public function it_can_get_overdue_assignments()
    {
        // Create overdue assignment
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->subDays(5)->toDateString(),
            'expected_return_date' => now()->subDays(1)->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        // Create non-overdue assignment
        ProductVendorAssignment::create([
            'product_id' => Product::factory()->create()->id,
            'vendor_id' => $this->vendor->id,
            'assignment_type' => 'repair',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'expected_return_date' => now()->addDays(2)->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $overdueAssignments = VendorService::getOverdueAssignments();

        $this->assertCount(1, $overdueAssignments);
        $this->assertEquals($this->product->id, $overdueAssignments->first()->product_id);
    }

    /** @test */
    public function it_gets_correct_assignment_status_for_different_types()
    {
        $testCases = [
            'cleaning' => 'under_cleaning',
            'repair' => 'under_repair',
            'alteration' => 'under_alteration',
            'maintenance' => 'under_maintenance'
        ];

        foreach ($testCases as $assignmentType => $expectedStatus) {
            $product = Product::factory()->create();
            
            VendorService::assignProductToVendor(
                $product->id,
                $this->vendor->id,
                ['assignment_type' => $assignmentType]
            );

            $status = ProductMaintenanceService::getCurrentStatus($product->id);
            $this->assertEquals($expectedStatus, $status['maintenance_status'], 
                "Failed for assignment type: {$assignmentType}");
        }
    }
}
