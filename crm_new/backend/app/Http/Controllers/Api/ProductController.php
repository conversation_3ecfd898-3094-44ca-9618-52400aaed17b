<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\ProductMaintenanceService;
use Mr<PERSON><PERSON>em\LaradrobeServices\Models\OrderReturn;
use Mr<PERSON><PERSON><PERSON>\LaradrobeServices\Models\Product;
use MrB<PERSON>em\LaradrobeServices\Enums\ProductStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use MrBohem\LaradrobeServices\VendorService;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with([
            'user.profile',
            'product_image' => function($query) {
                $query->orderBy('created_at', 'desc')->limit(3);
            },
            'productAccessories.images' => function($query) {
                $query->orderBy('created_at', 'desc')->limit(2);
            },
            'latestMaintenanceStatus', // Eager load latest maintenance status
            'activeVendorAssignment' // Eager load active vendor assignment
        ]);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%")
                  ->orWhere('brand_name', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($user) use ($search) {
                      $user->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by category
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Condition filter - filter products by maintenance status
        if ($request->has('condition') && $request->condition !== 'all') {
            $this->applyConditionFilter($query, $request->condition);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by city (through user profile)
        if ($request->has('city')) {
            $query->whereHas('user.profile', function ($profile) use ($request) {
                $profile->where('city', $request->city);
            });
        }

        // Date availability filter - only process when both dates are provided
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        if ($fromDate && $toDate) {
            // Validate dates
            try {
                $fromDate = \Carbon\Carbon::parse($fromDate)->format('Y-m-d');
                $toDate = \Carbon\Carbon::parse($toDate)->format('Y-m-d');
            } catch (\Exception $e) {
                return response()->json([
                    'error' => 'Invalid date format. Please use YYYY-MM-DD format.'
                ], 400);
            }

            // Add rental information to products
            $query->with(['Order' => function ($orderQuery) use ($fromDate, $toDate) {
                $orderQuery->where(function ($q) use ($fromDate, $toDate) {
                    $q->where(function ($dateQuery) use ($fromDate, $toDate) {
                        // Check if requested dates overlap with existing rentals
                        $dateQuery->where('from', '<=', $toDate)
                                 ->where('to', '>=', $fromDate);
                    });
                })->whereIn('status', ['confirmed', 'picked_up', 'delivered'])
                  ->with('user.profile')
                  ->orderBy('from', 'asc');
            }]);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $products = $query->paginate($request->get('per_page', 15));

        // Add availability status to each product if date filter is applied
        if ($fromDate && $toDate) {
            $products->getCollection()->transform(function ($product) use ($fromDate, $toDate) {
                $conflictingOrders = $product->Order->filter(function ($order) use ($fromDate, $toDate) {
                    return $order->from <= $toDate && $order->to >= $fromDate;
                });

                $product->availability_status = $conflictingOrders->isEmpty() ? 'available' : 'rented';
                $product->conflicting_orders = $conflictingOrders->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'from' => $order->from,
                        'to' => $order->to,
                        'status' => $order->status,
                        'customer_name' => $order->user->profile->name ?? $order->user->name ?? 'Unknown',
                        'customer_email' => $order->user->email ?? 'Unknown'
                    ];
                });

                // Remove the full orders relationship to keep response clean
                unset($product->Order);

                return $product;
            });

            // Add filter information to response
            $response = $products->toArray();
            $response['filters'] = [
                'from_date' => $fromDate,
                'to_date' => $toDate,
            ];

            return response()->json($response);
        }

        // Add maintenance status and vendor information to all products using eager loaded data
        $products->getCollection()->transform(function ($product) {
            $product->maintenance_status = $this->getProductMaintenanceStatusFromEagerLoaded($product);
            $product->vendor_info = $this->getVendorInfoFromEagerLoaded($product);
            return $product;
        });

        return response()->json($products);
    }

    public function show(Product $product)
    {
        return response()->json([
            'product' => $product->load([
                'user.profile',
                'product_image',
                'product_accessories.product_accessory_images',
                'orders.user'
            ])
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:200',
            'category' => 'required|string|max:255',
            'purchase_cost' => 'nullable|integer|min:0',
            'rental_cost' => 'required|integer|min:0',
            'security_deposit_cost' => 'nullable|integer|min:0',
            'rental_days' => 'nullable|integer|min:1|max:365',
            'brand_name' => 'nullable|string|max:100',
            'size' => 'nullable|string|max:10',
            'color' => 'nullable|string|max:30',
            'feature' => 'nullable|string|max:20',
            'on_store' => 'boolean',
        ]);

        $product = Product::create($request->all());

        return response()->json([
            'product' => $product->load(['user.profile', 'category']),
            'message' => 'Product created successfully'
        ], 201);
    }

    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:200',
            'category' => 'required|string|max:255',
            'purchase_cost' => 'nullable|integer|min:0',
            'rental_cost' => 'required|integer|min:0',
            'security_deposit_cost' => 'nullable|integer|min:0',
            'rental_days' => 'nullable|integer|min:1|max:365',
            'brand_name' => 'nullable|string|max:100',
            'size' => 'nullable|string|max:10',
            'color' => 'nullable|string|max:30',
            'feature' => 'nullable|string|max:20',
            'on_store' => 'boolean',
        ]);

        $product->update($request->all());

        return response()->json([
            'product' => $product->load(['user.profile', 'product_image', 'category']),
            'message' => 'Product updated successfully'
        ]);
    }

    public function destroy(Product $product)
    {
        $product->delete();

        return response()->json([
            'message' => 'Product deleted successfully'
        ]);
    }

    public function restore($id)
    {
        $product = Product::withTrashed()->findOrFail($id);
        $product->restore();

        return response()->json([
            'product' => $product->load(['user.profile', 'images', 'category']),
            'message' => 'Product restored successfully'
        ]);
    }

    public function forceDelete($id)
    {
        $product = Product::withTrashed()->findOrFail($id);
        $product->forceDelete();

        return response()->json([
            'message' => 'Product permanently deleted'
        ]);
    }

    /**
     * Apply condition filter to products query using the professional maintenance service
     */
    private function applyConditionFilter($query, $condition)
    {
        $productIds = ProductMaintenanceService::getProductsByStatus($condition);

        if (!empty($productIds)) {
            $query->whereIn('id', $productIds);
        } else {
            // If no products found with this status, return empty result
            $query->whereRaw('1 = 0');
        }
    }

    /**
     * Update product maintenance status using professional service architecture
     */
    public function updateMaintenanceStatus(Request $request, $id)
    {
        $validStatuses = implode(',', ProductStatus::values());
        $request->validate([
            'action' => "required|in:{$validStatuses}",
            'notes' => 'nullable|string|max:500'
        ]);

        $product = Product::findOrFail($id);

        try {
            // Use the professional service to update maintenance status
            $maintenanceStatus = ProductMaintenanceService::updateStatusManually(
                $product->id,
                $request->action,
                $request->notes,
                auth()->id()
            );

            // Load the product with its latest maintenance status
            $product->load(['user.profile', 'productAccessories']);

            return response()->json([
                'message' => 'Product maintenance status updated successfully',
                'product' => $product,
                'maintenance_status' => $maintenanceStatus->getStatusSummary()
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to update product maintenance status', [
                'product_id' => $product->id,
                'action' => $request->action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to update maintenance status: ' . $e->getMessage(),
                'error' => true
            ], 500);
        }
    }

    /**
     * Get product maintenance status using the centralized maintenance service
     */
    /**
     * Get vendor info using eager loaded data (optimized - no N+1 queries)
     */
    public function getVendorInfoFromEagerLoaded($product)
    {
        $assignment = $product->activeVendorAssignment;

        if ($assignment) {
            return [
                'is_with_vendor' => true,
                'vendor_name' => $assignment->vendor->name,
                'vendor_type' => $assignment->vendor->type,
                'assignment_type' => $assignment->assignment_type,
                'assigned_date' => $assignment->assigned_date,
                'expected_return_date' => $assignment->expected_return_date,
                'assignment_id' => $assignment->id
            ];
        }

        return ['is_with_vendor' => false];
    }

    /**
     * Get maintenance status using eager loaded data (optimized - no N+1 queries)
     */
    public function getProductMaintenanceStatusFromEagerLoaded($product)
    {
        $latestStatus = $product->latestMaintenanceStatus;

        if (!$latestStatus) {
            // Use centralized status mapping for default
            $defaultStatus = \MrBohem\LaradrobeServices\ProductMaintenanceService::getStatusInfo('ready');
            return [
                'status' => 'ready',
                'label' => $defaultStatus['label'],
                'color' => $defaultStatus['color'],
                'is_clean' => true,
                'notes' => null,
                'last_cleaned_at' => null,
                'last_maintained_at' => null,
            ];
        }

        // Use the model's getStatusSummary method which uses centralized mapping
        $summary = $latestStatus->getStatusSummary();

        return [
            'status' => $summary['status'],
            'label' => $summary['label'],
            'color' => $summary['color'],
            'is_clean' => $summary['is_clean'],
            'notes' => $summary['notes'],
            'last_cleaned_at' => $latestStatus->last_cleaned_at,
            'last_maintained_at' => $latestStatus->last_maintained_at,
        ];
    }

    /**
     * Legacy method - kept for backward compatibility but should be avoided due to N+1 queries
     * @deprecated Use getProductMaintenanceStatusFromEagerLoaded instead
     */
    public function getProductMaintenanceStatus($product)
    {
        $latestStatus = \MrBohem\LaradrobeServices\Models\ProductMaintenanceStatus::getLatestForProduct($product->id);

        if (!$latestStatus) {
            // Use centralized status mapping for default
            $defaultStatus = \MrBohem\LaradrobeServices\ProductMaintenanceService::getStatusInfo('ready');
            return [
                'status' => 'ready',
                'label' => $defaultStatus['label'],
                'color' => $defaultStatus['color'],
                'is_clean' => true,
                'notes' => null,
                'last_cleaned_at' => null,
                'last_maintained_at' => null,
            ];
        }

        // Use the model's getStatusSummary method which uses centralized mapping
        $summary = $latestStatus->getStatusSummary();

        return [
            'status' => $summary['status'],
            'label' => $summary['label'],
            'color' => $summary['color'],
            'is_clean' => $summary['is_clean'],
            'notes' => $summary['notes'],
            'last_cleaned_at' => $latestStatus->last_cleaned_at,
            'last_maintained_at' => $latestStatus->last_maintained_at,
        ];
    }
}
