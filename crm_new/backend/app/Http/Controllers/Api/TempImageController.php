<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use MrBohem\LaradrobeServices\LaradrobeServices;

class TempImageController extends Controller
{
    /**
     * Get all temp images from S3 TempImage folder
     */
    public function index()
    {
        try {
            // Get files from S3 TempImage folder
            $s3Files = Storage::disk('s3')->files('TempImage');

            $tempImages = [];
            foreach ($s3Files as $file) {
                // Extract filename from path
                $filename = basename($file);

                // Skip if not an image file
                $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    continue;
                }

                $tempImages[] = [
                    'filename' => $filename,
                    'path' => $file,
                    'url' => Storage::disk('s3')->url($file),
                    'size' => Storage::disk('s3')->size($file),
                    'last_modified' => Storage::disk('s3')->lastModified($file)
                ];
            }

            // Sort by last modified (newest first)
            usort($tempImages, function($a, $b) {
                return $b['last_modified'] - $a['last_modified'];
            });

            return response()->json([
                'success' => true,
                'temp_images' => $tempImages,
                'total_count' => count($tempImages),
                'total_size' => array_sum(array_column($tempImages, 'size'))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch temp images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a single temp image
     */
    public function destroy($filename)
    {
        try {
            $filePath = "TempImage/{$filename}";

            // Check if file exists
            if (!Storage::disk('s3')->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found'
                ], 404);
            }

            // Delete the file from S3
            Storage::disk('s3')->delete($filePath);

            // Also delete background-removed version if it exists
            $bgRemovedPath = "TempImage/rb-{$filename}";
            if (Storage::disk('s3')->exists($bgRemovedPath)) {
                Storage::disk('s3')->delete($bgRemovedPath);
            }

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully',
                'deleted_file' => $filename
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk delete temp images
     */
    public function bulkDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filenames' => 'required|array|min:1',
            'filenames.*' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $deletedFiles = [];
            $failedFiles = [];

            foreach ($request->filenames as $filename) {
                $filePath = "TempImage/{$filename}";

                if (Storage::disk('s3')->exists($filePath)) {
                    // Delete the main file
                    Storage::disk('s3')->delete($filePath);
                    
                    // Also delete background-removed version if it exists
                    $bgRemovedPath = "TempImage/rb-{$filename}";
                    if (Storage::disk('s3')->exists($bgRemovedPath)) {
                        Storage::disk('s3')->delete($bgRemovedPath);
                    }
                    
                    $deletedFiles[] = $filename;
                } else {
                    $failedFiles[] = $filename;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk delete completed',
                'deleted_files' => $deletedFiles,
                'failed_files' => $failedFiles,
                'deleted_count' => count($deletedFiles),
                'failed_count' => count($failedFiles)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove background from temp image using Adobe API
     */
    public function removeBackground(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'filename' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $filename = $request->filename;
            $filePath = "TempImage/{$filename}";

            // Check if file exists
            if (!Storage::disk('s3')->exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found'
                ], 404);
            }

            // Use LaradrobeServices to remove background
            $laradrobeServices = new LaradrobeServices();
            $result = $laradrobeServices->removeBg($filename);

            // Check if background-removed file was created
            $bgRemovedPath = "TempImage/rb-{$filename}";
            $bgRemovedExists = Storage::disk('s3')->exists($bgRemovedPath);

            return response()->json([
                'success' => true,
                'message' => 'Background removal initiated successfully',
                'original_file' => $filename,
                'background_removed_file' => $bgRemovedExists ? "rb-{$filename}" : null,
                'background_removed_url' => $bgRemovedExists ? Storage::disk('s3')->url($bgRemovedPath) : null,
                'note' => 'Background removal may take a few moments to complete'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove background',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get temp image statistics
     */
    public function getStats()
    {
        try {
            $s3Files = Storage::disk('s3')->files('TempImage');
            
            $imageFiles = [];
            $totalSize = 0;
            $oldestTimestamp = null;
            $newestTimestamp = null;

            foreach ($s3Files as $file) {
                $filename = basename($file);
                $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $size = Storage::disk('s3')->size($file);
                    $lastModified = Storage::disk('s3')->lastModified($file);
                    
                    $imageFiles[] = $file;
                    $totalSize += $size;
                    
                    if ($oldestTimestamp === null || $lastModified < $oldestTimestamp) {
                        $oldestTimestamp = $lastModified;
                    }
                    
                    if ($newestTimestamp === null || $lastModified > $newestTimestamp) {
                        $newestTimestamp = $lastModified;
                    }
                }
            }

            return response()->json([
                'success' => true,
                'stats' => [
                    'total_images' => count($imageFiles),
                    'total_size' => $totalSize,
                    'total_size_formatted' => $this->formatBytes($totalSize),
                    'oldest_image' => $oldestTimestamp,
                    'newest_image' => $newestTimestamp,
                    'oldest_image_formatted' => $oldestTimestamp ? date('Y-m-d H:i:s', $oldestTimestamp) : null,
                    'newest_image_formatted' => $newestTimestamp ? date('Y-m-d H:i:s', $newestTimestamp) : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search temp images by filename
     */
    public function search(Request $request)
    {
        $searchTerm = $request->get('q', '');

        try {
            $s3Files = Storage::disk('s3')->files('TempImage');
            $tempImages = [];

            foreach ($s3Files as $file) {
                $filename = basename($file);
                $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                
                if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    continue;
                }

                // Filter by search term
                if ($searchTerm && stripos($filename, $searchTerm) === false) {
                    continue;
                }

                $tempImages[] = [
                    'filename' => $filename,
                    'path' => $file,
                    'url' => Storage::disk('s3')->url($file),
                    'size' => Storage::disk('s3')->size($file),
                    'last_modified' => Storage::disk('s3')->lastModified($file)
                ];
            }

            // Sort by last modified (newest first)
            usort($tempImages, function($a, $b) {
                return $b['last_modified'] - $a['last_modified'];
            });

            return response()->json([
                'success' => true,
                'temp_images' => $tempImages,
                'search_term' => $searchTerm,
                'total_count' => count($tempImages)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to search images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clean old temp images
     */
    public function cleanup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'older_than_days' => 'integer|min:1|max:365'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $olderThanDays = $request->get('older_than_days', 30);
            $cutoffTimestamp = time() - ($olderThanDays * 24 * 60 * 60);
            
            $s3Files = Storage::disk('s3')->files('TempImage');
            $deletedFiles = [];

            foreach ($s3Files as $file) {
                $filename = basename($file);
                $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                
                if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    continue;
                }

                $lastModified = Storage::disk('s3')->lastModified($file);
                
                if ($lastModified < $cutoffTimestamp) {
                    Storage::disk('s3')->delete($file);
                    $deletedFiles[] = $filename;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Cleanup completed successfully',
                'deleted_files' => $deletedFiles,
                'deleted_count' => count($deletedFiles),
                'older_than_days' => $olderThanDays
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cleanup images',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
