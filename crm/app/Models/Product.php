<?php

namespace App\Models;

use App\Models\User;
use App\Models\Genral\Shop;
use App\Models\Master\MstProduct;
use App\Models\Master\MstCategory;
use App\Models\Master\MstProductImage;
use App\Models\Master\MstProductDetail;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Master\MstProductSpecification;

class Product extends Model
{
    use SoftDeletes,HasUuids;
    protected $fillable = [
        'user_id',
        'category',
        'name',
        'purchase_cost',
        'rental_cost',
        'security_deposit_cost',
        'rental_days',
        'brand_name',
        'slug',
        'thumbnail',
        'size',
        'color',
        'views',
        'feature',
        'on_store'
    ];

    protected $date = ['deleted_at'];
    /* End Variables */

    protected function name():Attribute
    {
        return Attribute::make(
            set: fn (string $value) => ucwords(strtolower($value)),
        );
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function cartItems()
    {
        return $this->hasMany(Cart::class);
    }

    public function mst_product_detail()
    {
        return $this->hasOne(MstProductDetail::class, 'mst_product_id', 'mst_product_id');
    }

    public function mst_product_specification()
    {
        return $this->hasMany(MstProductSpecification::class, 'mst_product_id', 'mst_product_id');
    }
    /* End Master Models*/


    /* Genral Models Relations*/
    public function shop()
    {
        return $this->belongsTo(Shop::class);
    }

    public function product_category()
    {
        return $this->belongsTo(ProductCategory::class);
    }

    public function product_image()
    {
        return $this->hasMany(ProductImage::class);
    }

    public function product_detail()
    {
        return $this->hasOne(ProductDetail::class, 'product_id', 'id');
    }

    public function product_specification()
    {
        return $this->hasMany(ProductSpecification::class, 'product_id', 'id');
    }

    public function mst_category_product_category(){
        return $this->hasOneThrough(MstCategory::class, ProductCategory::class);
    }
    /* End Genral Models */

    public function productEnroll(){
        return $this->hasOne(ProductEnroll::class);
    }

    public function productAccessories(){
        return $this->hasMany(ProductAccessory::class);
    }

    public function Order(){
        return $this->hasMany(Order::class);
    }


    /* GET Functions */
    public static function getProductById($field, $shop_id)
    {
        return self::where($field, '=', $shop_id)->get();
    }

    public static function getPhoneHomeProduct($shopsID){
        return self::with(['mstProductImage','shop'])
                    ->whereIn('shop_id', $shopsID)
                    ->get()
                    ->random(1);
    }

    public static function getProductsByShop($shopID, $productCategoryID)
    {
        return self::with([
                    'productDetail', 
                    'mstProductImage', 
                    'mstProductDetail',  
                    'mstProductSpecification'
                ])
                ->where('shop_id', $shopID)
                ->where('product_category', $productCategoryID)
                ->get();
    }

    // public static function getProductFullDetail($productID)
    // {
    //     return self::with([
    //             'productDetail',
    //             'productImage',
    //             'mstProductImage',
    //             'mstProductDetail',
    //             'mstProductSpecification'
    //         ])
    //         ->find($productID);
    // }
    
    public static function getProductFullDetail($productId)
    {
		return self::with([
                        'product_detail',
                        'product_image',
                        'product_specification',
                        'mst_product_image',
                        'mst_product_detail',
                        'mst_product_specification',
                        'mst_product_specification.mst_specification',
                        'mst_product_specification.mst_specification.mstCategorySpecification'
                    ])->find($productId);
    }

    public function increaseViews(){
        $this->increment('views');
    }

    public function scopeSearch($query, $value)
     {
          if($value == null) return $query;
          $query->Join('users', 'users.id', 'products.user_id')
               ->where(function($q) use($value){
                   $q->orwhereLike('users.name', '%'.$value.'%');
                   $q->orwhereLike('products.name', '%'.$value.'%');
               });
               
          return $query;
     }
    
    /* END GET Functions */
}
