import 'dart:convert';

class Vendor {
  final String id;
  final String name;
  final String? type;
  final String? contactPerson;
  final String? phone;
  final String? email;
  final String? address;
  final String? ratePerItem;
  final String? specialties;
  final String? status;
  final String? notes;
  final int? assignmentsCount;
  final int? activeAssignmentsCount;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Vendor({
    required this.id,
    required this.name,
    this.type,
    this.contactPerson,
    this.phone,
    this.email,
    this.address,
    this.ratePerItem,
    this.specialties,
    this.status,
    this.notes,
    this.assignmentsCount,
    this.activeAssignmentsCount,
    this.createdAt,
    this.updatedAt,
  });

  factory Vendor.fromMap(Map<String, dynamic> map) {
    return Vendor(
      id: map['id']?.toString() ?? '',
      name: map['name'] ?? '',
      type: map['type'],
      contactPerson: map['contact_person'],
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      ratePerItem: map['rate_per_item'],
      specialties: map['specialties'],
      status: map['status'],
      notes: map['notes'],
      assignmentsCount: map['assignments_count']?.toInt(),
      activeAssignmentsCount: map['active_assignments_count']?.toInt(),
      createdAt: map['created_at'] != null ? DateTime.tryParse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.tryParse(map['updated_at']) : null,
    );
  }

  factory Vendor.fromJson(String source) => Vendor.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'contact_person': contactPerson,
      'phone': phone,
      'email': email,
      'address': address,
      'rate_per_item': ratePerItem,
      'specialties': specialties,
      'status': status,
      'notes': notes,
      'assignments_count': assignmentsCount,
      'active_assignments_count': activeAssignmentsCount,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  String get fullAddress {
    return address ?? '';
  }

  String get typeLabel {
    switch (type?.toLowerCase()) {
      case 'dry_cleaner':
        return 'Dry Cleaner';
      case 'tailor':
        return 'Tailor';
      case 'repair_shop':
        return 'Repair Shop';
      case 'maintenance':
        return 'Maintenance';
      default:
        return type ?? 'General';
    }
  }

  String get formattedRate {
    if (ratePerItem != null) {
      return '₹${ratePerItem}/item';
    }
    return 'Rate not specified';
  }

  String get statusLabel {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'suspended':
        return 'Suspended';
      default:
        return status ?? 'Unknown';
    }
  }
}
