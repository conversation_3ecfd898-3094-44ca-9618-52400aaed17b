<?php

namespace App\Filament\Resources\ProductImageRelationManagerResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;
use Storage;

class ProductImageRelationManager extends RelationManager
{
    protected static string $relationship = 'product_image';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('image_name')
                    ->image()
                    ->required()
                    ->disk('s3')
                    ->getUploadedFileNameForStorageUsing(function ($file) {
                        // dd($file);
                        return LaradrobeServices::uploadProductImage($file);
                    }),
                
                Forms\Components\Hidden::make('user_id')
                    ->default(fn (RelationManager $livewire) => $livewire->getOwnerRecord()->user_id),
    
                Forms\Components\Hidden::make('product_id')
                    ->default(fn (RelationManager $livewire) => $livewire->getOwnerRecord()->id),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_name')
                    ->label('Image')
                    ->getStateUsing(function ($record) {
                        return Storage::disk('s3')->url("Product/" . $record->image_name);
                    }),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ]);
    }
}
