<?php

namespace Database\Factories;

use App\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

class VendorFactory extends Factory
{
    protected $model = Vendor::class;

    public function definition()
    {
        return [
            'name' => $this->faker->company . ' ' . $this->faker->randomElement(['Dry Cleaners', 'Repairs', 'Tailors']),
            'type' => $this->faker->randomElement(['dry_cleaner', 'repair_shop', 'tailor', 'alteration_shop']),
            'contact_person' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->unique()->safeEmail,
            'address' => $this->faker->address,
            'rate_per_item' => $this->faker->randomFloat(2, 10, 100),
            'specialties' => $this->faker->sentence,
            'notes' => $this->faker->optional()->paragraph,
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'inactive',
            ];
        });
    }

    public function dryCleaners()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'dry_cleaner',
                'name' => $this->faker->company . ' Dry Cleaners',
                'specialties' => 'Professional dry cleaning services',
            ];
        });
    }

    public function repairShop()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'repair_shop',
                'name' => $this->faker->company . ' Repairs',
                'specialties' => 'Clothing repair and restoration',
            ];
        });
    }

    public function tailor()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'tailor',
                'name' => $this->faker->company . ' Tailors',
                'specialties' => 'Custom tailoring and alterations',
            ];
        });
    }
}
