<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use MrBohem\LaradrobeServices\ProductTimelineService;
use MrBohem\LaradrobeServices\Models\Product;

class ProductTimelineController extends Controller
{
    /**
     * Get complete timeline for a product
     */
    public function getTimeline($productId)
    {
        try {
            // Verify product exists
            $product = Product::find($productId);
            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            // Get timeline data
            $timeline = ProductTimelineService::getProductTimeline($productId);
            $stats = ProductTimelineService::getTimelineStats($productId);

            return response()->json([
                'success' => true,
                'data' => [
                    'product' => [
                        'id' => $product->id,
                        'name' => $product->name,
                        'category' => $product->category,
                        'thumbnail' => $product->thumbnail
                    ],
                    'timeline' => $timeline,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch product timeline',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get timeline events by type
     */
    public function getTimelineByType($productId, $type)
    {
        try {
            $product = Product::find($productId);
            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            $timeline = ProductTimelineService::getProductTimeline($productId);
            
            // Filter by type
            $filteredTimeline = array_filter($timeline, function($event) use ($type) {
                return $event['type'] === $type;
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'product' => [
                        'id' => $product->id,
                        'name' => $product->name
                    ],
                    'timeline' => array_values($filteredTimeline),
                    'type' => $type
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch timeline by type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get timeline summary
     */
    public function getTimelineSummary($productId)
    {
        try {
            $product = Product::find($productId);
            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found'
                ], 404);
            }

            $stats = ProductTimelineService::getTimelineStats($productId);

            return response()->json([
                'success' => true,
                'data' => [
                    'product' => [
                        'id' => $product->id,
                        'name' => $product->name
                    ],
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch timeline summary',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
