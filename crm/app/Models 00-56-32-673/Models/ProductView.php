<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductView extends Model
{
    use HasFactory;

    protected $fillable = ['user_id','product_id'];

    public static function manageProductViews($productId,$userId){

        $isProductViewed = ProductView::where('user_id', $userId)
                ->where('product_id', $productId)
                ->whereDay('created_at', now()->day)
                ->exists();

        if(!$isProductViewed){
            $product = Product::find($productId);
            $product->increment('views');
        }

        ProductView::create([
            'user_id'=> $userId,
            'product_id'=> $productId,
        ]);
    }
}
