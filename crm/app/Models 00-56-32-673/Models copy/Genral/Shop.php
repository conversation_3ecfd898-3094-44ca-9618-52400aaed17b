<?php

namespace App\Models\Genral;

use App\User;
use App\Models\Genral\ShopImage;
use App\Models\Genral\ProductCategory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Shop extends Model
{
	use SoftDeletes;
	
    protected $guarded = [];

    protected $date = ['deleted_at'];
    
    public function user()
    {
    	return $this->belongsTo(User::class);
    }

    public function shop_image()
    {
        return $this->hasMany(ShopImage::class, 'shop_id', 'id');
    }

    public function product_category()
    {
        return $this->hasMany(ProductCategory::class, 'shop_id', 'id');
    }

    public function product()
    {
        return $this->hasMany(Product::class, 'shop_id', 'id');
    }

    public function product_detail()
    {
        return $this->hasMany(ProductDetail::class);
    }


    public static function getPhoneHomeShop($city)
    {
        return Shop::with([
                    'shopImage' => function ($query) {
                        $query->where('thumbnail', 1);
                    }
                ])->where('shop_city', $city)->get();
    }
    
    public static function getShopProductDetailById($id)
    {
        return self::select('*')
                ->join('products','products.shop_id','=','shops.id')
                ->get();
    }
}
