<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Cart extends Model
{
    use HasFactory,HasUuids,SoftDeletes;

    protected $fillable = ['user_id','product_id'];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
