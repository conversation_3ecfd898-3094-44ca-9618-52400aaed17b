/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #f8fafc;
    color: #1e293b;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Layout */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 240px;
    height: 100vh;
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.1);
}

.main-content {
    margin-left: 240px;
    min-height: 100vh;
    background: #f8fafc;
}

/* Sidebar Header */
.sidebar-header {
    padding: 16px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.logo-text h1 {
    color: white;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 2px;
}

.logo-text span {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: 12px 12px;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    color: #94a3b8;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.nav-link i {
    font-size: 16px;
    width: 16px;
    text-align: center;
}

.badge {
    background: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
}

.badge.danger {
    background: #ef4444;
    animation: pulse 2s infinite;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.user-info {
    flex: 1;
}

.user-name {
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.user-email {
    color: #94a3b8;
    font-size: 10px;
}

/* Top Header */
.top-header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid #e2e8f0;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    color: #64748b;
    cursor: pointer;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #94a3b8;
    font-size: 14px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 13px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-btn {
    width: 36px;
    height: 36px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    font-size: 14px;
}

.header-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #374151;
}

.notification-dot {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 6px;
    height: 6px;
    background: #ef4444;
    border-radius: 50%;
    border: 1px solid white;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-menu:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-details .user-name {
    font-weight: 600;
    font-size: 12px;
    color: #1e293b;
}

.user-role {
    font-size: 10px;
    color: #64748b;
}

/* Dashboard Content */
.dashboard-content {
    padding: 16px;
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.page-title h1 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.page-title p {
    font-size: 14px;
    color: #64748b;
}

.page-actions {
    display: flex;
    gap: 8px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: white;
    color: #64748b;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.gradient-blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-purple {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.gradient-orange {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 28px;
    font-weight: 800;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 13px;
    opacity: 0.9;
    margin-bottom: 8px;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.stat-change.positive {
    color: rgba(255, 255, 255, 0.9);
}

/* Order Stats */
.order-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.order-stat-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.order-stat-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.order-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.order-icon.pending {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.order-icon.processing {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.order-icon.completed {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.order-number {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 2px;
}

.order-label {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 16px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 16px;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.card-header p {
    color: #64748b;
    font-size: 13px;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.activity-item:hover {
    background: #f1f5f9;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.activity-icon.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.activity-icon.info {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.activity-icon.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.activity-icon.star {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.activity-title {
    font-size: 14px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.activity-desc {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 4px;
}

.activity-time {
    font-size: 11px;
    color: #94a3b8;
}

/* Dashboard Sidebar */
.dashboard-sidebar {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.action-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.action-btn.secondary {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.action-btn.secondary:hover {
    background: #f1f5f9;
    color: #374151;
}

/* Top Products */
.top-products {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.product-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.product-item:hover {
    background: #f1f5f9;
}

.product-rank {
    width: 28px;
    height: 28px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 12px;
    color: white;
}

.rank-1 {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.rank-2 {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.rank-3 {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
    font-size: 13px;
}

.product-views {
    font-size: 11px;
    color: #64748b;
}

.product-trend {
    font-weight: 600;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 6px;
}

.product-trend.positive {
    background: #dcfce7;
    color: #166534;
}

.product-trend.negative {
    background: #fee2e2;
    color: #991b1b;
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 240px;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .search-box {
        width: 180px;
    }

    .dashboard-content {
        padding: 12px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 16px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .order-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .dashboard-grid {
        gap: 12px;
    }
}
