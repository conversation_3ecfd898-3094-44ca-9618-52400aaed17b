<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/crm/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/crm/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/crm/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/crm/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/crm/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/app/Services" isTestSource="false" packagePrefix="App\Services\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/laravel/tests" isTestSource="true" packagePrefix="Tests\" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/anourvalar/eloquent-serialize" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/blade-ui-kit/blade-heroicons" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/blade-ui-kit/blade-icons" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/danharrin/date-format-converter" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/danharrin/livewire-rate-limiting" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/actions" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/filament" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/forms" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/infolists" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/notifications" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/support" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/tables" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filament/widgets" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/guava/filament-nested-resources" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/intervention/gif" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/intervention/image-laravel" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/kirschbaum-development/eloquent-power-joins" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/pail" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/telescope" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/csv" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/flysystem-aws-s3-v3" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/uri" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/league/uri-interfaces" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/livewire/livewire" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/masterminds/html5" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/mrbohem/laradrobe-services" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/mrbohem/laramage" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/openspout/openspout" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/ryangjchandler/blade-capture-directive" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/spatie/color" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/spatie/invade" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/spatie/laravel-package-tools" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/staabm/side-effects-detector" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/clock" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/html-sanitizer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/crm/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/abraham/twitteroauth" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/aws/aws-crt-php" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/aws/aws-sdk-php" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/barryvdh/laravel-debugbar" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/beste/clock" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/beste/in-memory-cache" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/beste/json" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/brianium/paratest" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/clue/redis-protocol" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/clue/redis-react" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/doctrine/sql-formatter" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/evenement/evenement" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/fidry/cpu-core-counter" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/fig/http-message-util" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/firebase/php-jwt" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/auth" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/cloud-core" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/cloud-storage" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/common-protos" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/gax" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/grpc-gcp" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/longrunning" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/google/protobuf" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/grpc/grpc" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/intervention/gif" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/intervention/image-laravel" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/jean85/pretty-package-versions" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/kreait/firebase-php" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/kreait/firebase-tokens" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/kreait/laravel-firebase" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel-notification-channels/fcm" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/pulse" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/reverb" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/telescope" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/lcobucci/jwt" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/flysystem-aws-s3-v3" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/oauth1-client" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/uri" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/league/uri-interfaces" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/livewire/livewire" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/livewire/volt" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/mrbohem/laradrobe-services" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/mrbohem/laramage" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/mtdowling/jmespath.php" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/paragonie/constant_time_encoding" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/paragonie/sodium_compat" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/pestphp/pest" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/pestphp/pest-plugin" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/pestphp/pest-plugin-arch" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/pestphp/pest-plugin-laravel" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/php-debugbar/php-debugbar" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpseclib/phpseclib" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/pusher/pusher-php-server" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/ratchet/rfc6455" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/cache" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/dns" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/event-loop" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/promise" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/promise-timer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/socket" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/react/stream" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/rize/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/backtrace" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/db-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/error-solutions" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/laravel-backup" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/laravel-ignition" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/laravel-package-tools" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/laravel-signal-aware-command" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/spatie/temporary-directory" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/cache" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/cache-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/clock" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/var-exporter" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/ta-tikoma/phpunit-architecture-test" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/laravel/vendor/webmozart/assert" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>