<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Http;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\Facades\LaradrobeServices;
use Storage;
use Filament\Notifications\Notification;

class TempImage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.temp-image';

    protected static ?string $navigationLabel = 'Temp Images';

    public $images = [];

    public function mount(): void
    {
        $this->loadImages();
    }

    public function loadImages(): void
    {
        $files = Storage::disk('s3')->files('TempImage');
        $this->images = collect($files);
    }

    public function removeBackground($imageUrl): void
    {
            LaradrobeServices::removeBg($imageUrl);
            $url = Storage::disk('s3')->url("TempImage/". $imageUrl);
            $removedUrl = Storage::disk('s3')->url("TempImage/rb-".$imageUrl);
            dump($url,$removedUrl);

            $this->notify('success', 'Background removed successfully!');
    }

    public function deleteImage($fileName): void
    {
        if (Storage::disk('s3')->exists('TempImage/'.$fileName)) {
            Storage::disk('s3')->delete('TempImage/'.$fileName);
            Notification::make()
                ->title('Image deleted successfully.')
                ->success()
                ->send();
        } else {
            Notification::make()
            ->title('Image not found or already deleted successfully.')
            ->success()
            ->send();
        }

        $this->loadImages();
    }
}
