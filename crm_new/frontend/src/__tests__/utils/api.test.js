// Test utility functions and API helpers
describe('API Utilities', () => {
  // Mock localStorage
  const mockLocalStorage = {
    getItem: jest.fn(() => 'mock-token'),
    setItem: jest.fn(),
    removeItem: jest.fn()
  };
  Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

  beforeEach(() => {
    jest.clearAllMocks();
    global.fetch = jest.fn();
  });

  test('should create API headers with auth token', () => {
    // Mock the specific call
    mockLocalStorage.getItem.mockReturnValue('mock-token');

    const token = localStorage.getItem('authToken');
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    expect(headers['Authorization']).toBe('Bearer mock-token');
    expect(headers['Content-Type']).toBe('application/json');
  });

  test('should handle API response parsing', async () => {
    const mockResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({ data: [{ id: 1, name: 'Test' }] })
    };

    fetch.mockResolvedValue(mockResponse);

    const response = await fetch('http://localhost:8001/api/products');
    const data = await response.json();

    expect(fetch).toHaveBeenCalledWith('http://localhost:8001/api/products');
    expect(data).toEqual({ data: [{ id: 1, name: 'Test' }] });
  });

  test('should handle API errors', async () => {
    const mockResponse = {
      ok: false,
      status: 500,
      json: jest.fn().mockResolvedValue({ error: 'Server error' })
    };

    fetch.mockResolvedValue(mockResponse);

    const response = await fetch('http://localhost:8001/api/products');
    
    expect(response.ok).toBe(false);
    expect(response.status).toBe(500);
  });

  test('should format currency correctly', () => {
    const formatCurrency = (amount) => `₹${amount}`;
    
    expect(formatCurrency(100)).toBe('₹100');
    expect(formatCurrency(1500)).toBe('₹1500');
  });

  test('should format dates correctly', () => {
    const formatDate = (dateString) => new Date(dateString).toLocaleDateString();
    
    const testDate = '2025-07-09';
    const formatted = formatDate(testDate);
    
    expect(formatted).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
  });

  test('should validate status colors', () => {
    const statusColors = {
      'ready': 'green',
      'needs_cleaning': 'orange',
      'damaged': 'red',
      'under_cleaning': 'blue'
    };

    expect(statusColors['ready']).toBe('green');
    expect(statusColors['needs_cleaning']).toBe('orange');
    expect(statusColors['damaged']).toBe('red');
    expect(statusColors['under_cleaning']).toBe('blue');
  });

  test('should handle search filtering', () => {
    const products = [
      { id: 1, name: 'Pink Reception Lehnga' },
      { id: 2, name: 'Blue Wedding Dress' },
      { id: 3, name: 'Red Party Wear' }
    ];

    const searchTerm = 'Pink';
    const filtered = products.filter(p => 
      p.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    expect(filtered).toHaveLength(1);
    expect(filtered[0].name).toBe('Pink Reception Lehnga');
  });

  test('should handle status filtering', () => {
    const products = [
      { id: 1, status: 'ready' },
      { id: 2, status: 'needs_cleaning' },
      { id: 3, status: 'ready' }
    ];

    const readyProducts = products.filter(p => p.status === 'ready');
    const dirtyProducts = products.filter(p => p.status === 'needs_cleaning');

    expect(readyProducts).toHaveLength(2);
    expect(dirtyProducts).toHaveLength(1);
  });
});
