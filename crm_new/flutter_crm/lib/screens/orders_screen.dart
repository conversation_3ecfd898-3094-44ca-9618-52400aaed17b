import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_layout.dart';
import '../providers/data_provider.dart';
import '../models/order.dart';
import '../utils/app_theme.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({super.key});

  @override
  State<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadOrders();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    final dataProvider = Provider.of<DataProvider>(context, listen: false);
    await dataProvider.fetchOrders();
  }

  @override
  Widget build(BuildContext context) {
    return AppLayout(
      title: 'Orders',
      currentRoute: '/orders',
      child: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search orders...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value.toLowerCase();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                IconButton(
                  onPressed: _loadOrders,
                  icon: const Icon(Icons.refresh),
                  style: IconButton.styleFrom(
                    backgroundColor: AppTheme.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Orders List
          Expanded(
            child: Consumer<DataProvider>(
              builder: (context, dataProvider, child) {
                if (dataProvider.ordersLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (dataProvider.ordersError != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: AppTheme.error),
                        const SizedBox(height: 16),
                        Text('Error loading orders', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppTheme.gray900)),
                        const SizedBox(height: 8),
                        Text(dataProvider.ordersError!, style: TextStyle(color: AppTheme.gray600), textAlign: TextAlign.center),
                        const SizedBox(height: 16),
                        ElevatedButton(onPressed: _loadOrders, child: const Text('Retry')),
                      ],
                    ),
                  );
                }

                final orders = dataProvider.orders.where((order) {
                  if (_searchQuery.isEmpty) return true;
                  return order.id.toLowerCase().contains(_searchQuery) ||
                         (order.user?.name.toLowerCase().contains(_searchQuery) ?? false) ||
                         (order.product?.name.toLowerCase().contains(_searchQuery) ?? false);
                }).toList();

                if (orders.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.shopping_cart_outlined, size: 64, color: AppTheme.gray400),
                        const SizedBox(height: 16),
                        Text(_searchQuery.isEmpty ? 'No orders found' : 'No orders match your search', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppTheme.gray900)),
                        const SizedBox(height: 8),
                        Text(_searchQuery.isEmpty ? 'Orders will appear here' : 'Try adjusting your search terms', style: TextStyle(color: AppTheme.gray600)),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: _loadOrders,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: orders.length,
                    itemBuilder: (context, index) {
                      final order = orders[index];
                      return _buildOrderCard(order);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Order order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Order #${order.id}', style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16)),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Color(int.parse(order.statusColor.replaceFirst('#', '0xFF'))).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    order.statusLabel,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color(int.parse(order.statusColor.replaceFirst('#', '0xFF'))),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (order.user != null) Text('Customer: ${order.user!.name}', style: const TextStyle(color: AppTheme.gray600)),
            if (order.product != null) Text('Product: ${order.product!.name}', style: const TextStyle(color: AppTheme.gray600)),
            const SizedBox(height: 8),
            Row(
              children: [
                Text('${order.formattedFromDate} - ${order.formattedToDate}', style: const TextStyle(fontSize: 14, color: AppTheme.gray700)),
                const Spacer(),
                Text(order.formattedRentalCost, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: AppTheme.primary)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
