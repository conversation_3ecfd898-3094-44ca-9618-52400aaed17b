<?php

namespace App\Filament\Resources\ProductAccessoriesResource\Pages;

use App\Filament\Resources\ProductAccessoriesResource;
use App\Filament\Resources\ProductResource;
use Guava\FilamentNestedResources\Concerns\NestedPage;
use Guava\FilamentNestedResources\Pages\CreateRelatedRecord;

class CreateProductAccessoriesImage extends CreateRelatedRecord
{
    use NestedPage;

    protected static string $resource = ProductAccessoriesResource::class;

    protected static string $relationship = 'images';
}
