<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use MrBohem\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\ProductAccessory;

class ProductAccessoryController extends Controller
{
    public function index(Product $product)
    {
        $accessories = $product->productAccessories()->orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'accessories' => $accessories
        ]);
    }

    public function store(Request $request, Product $product)
    {
        $request->validate([
            'accessory' => 'required|string|max:100',
            'price' => 'nullable|integer|min:0',
        ]);

        try {
            $accessory = ProductAccessory::create([
                'product_id' => $product->id,
                'accessory' => $request->accessory,
                'price' => $request->price,
            ]);

            return response()->json([
                'message' => 'Accessory created successfully',
                'accessory' => $accessory
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create accessory: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Product $product, ProductAccessory $accessory)
    {
        // Ensure the accessory belongs to the product
        if ($accessory->product_id !== $product->id) {
            return response()->json([
                'message' => 'Accessory not found for this product'
            ], 404);
        }

        return response()->json([
            'accessory' => $accessory
        ]);
    }

    public function update(Request $request, Product $product, ProductAccessory $accessory)
    {
        // Ensure the accessory belongs to the product
        if ($accessory->product_id !== $product->id) {
            return response()->json([
                'message' => 'Accessory not found for this product'
            ], 404);
        }

        $request->validate([
            'accessory' => 'required|string|max:100',
            'price' => 'nullable|integer|min:0',
        ]);

        try {
            $accessory->update([
                'accessory' => $request->accessory,
                'price' => $request->price,
            ]);

            return response()->json([
                'message' => 'Accessory updated successfully',
                'accessory' => $accessory
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update accessory: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Product $product, ProductAccessory $accessory)
    {
        // Ensure the accessory belongs to the product
        if ($accessory->product_id !== $product->id) {
            return response()->json([
                'message' => 'Accessory not found for this product'
            ], 404);
        }

        try {
            $accessory->delete();

            return response()->json([
                'message' => 'Accessory deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete accessory: ' . $e->getMessage()
            ], 500);
        }
    }
}
