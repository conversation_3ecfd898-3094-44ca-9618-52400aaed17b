<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Mr<PERSON><PERSON>em\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\ProductMaintenanceStatus;
use MrB<PERSON>em\LaradrobeServices\ProductMaintenanceService;
use App\User;
use Laravel\Sanctum\Sanctum;

class ProductControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user and authenticate
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);
    }

    /** @test */
    public function it_can_get_all_products()
    {
        // Create test products
        $products = Product::factory()->count(3)->create();

        $response = $this->getJson('/api/products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'rental_cost',
                            'security_deposit',
                            'maintenance_status'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /** @test */
    public function it_can_filter_products_by_needs_attention()
    {
        // Create products with different statuses
        $dirtyProduct = Product::factory()->create(['name' => 'Dirty Product']);
        $cleanProduct = Product::factory()->create(['name' => 'Clean Product']);
        $damagedProduct = Product::factory()->create(['name' => 'Damaged Product']);

        // Set maintenance statuses
        ProductMaintenanceService::updateStatusManually($dirtyProduct->id, 'dirty', 'Test dirty product', $this->user->id);
        ProductMaintenanceService::updateStatusManually($cleanProduct->id, 'clean', 'Test clean product', $this->user->id);
        ProductMaintenanceService::updateStatusManually($damagedProduct->id, 'damaged', 'Test damaged product', $this->user->id);

        $response = $this->getJson('/api/products?condition=needs_attention');

        $response->assertStatus(200);
        
        $productNames = collect($response->json('data'))->pluck('name')->toArray();
        
        // Should include dirty and damaged products
        $this->assertContains('Dirty Product', $productNames);
        $this->assertContains('Damaged Product', $productNames);
        
        // Should NOT include clean product
        $this->assertNotContains('Clean Product', $productNames);
    }

    /** @test */
    public function it_excludes_products_currently_with_vendors()
    {
        $product = Product::factory()->create(['name' => 'Test Product']);
        
        // Mark as dirty (should show in needs_attention)
        ProductMaintenanceService::updateStatusManually($product->id, 'dirty', 'Test', $this->user->id);
        
        // Assign to vendor
        \App\ProductVendorAssignment::create([
            'product_id' => $product->id,
            'vendor_id' => \App\Vendor::factory()->create()->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $response = $this->getJson('/api/products?condition=needs_attention');

        $response->assertStatus(200);
        
        $productNames = collect($response->json('data'))->pluck('name')->toArray();
        
        // Should NOT include assigned product
        $this->assertNotContains('Test Product', $productNames);
    }

    /** @test */
    public function it_excludes_recently_returned_products()
    {
        $product = Product::factory()->create(['name' => 'Recently Returned Product']);
        
        // Mark as dirty
        ProductMaintenanceService::updateStatusManually($product->id, 'dirty', 'Test', $this->user->id);
        
        // Create a recent return record (within 4 hours)
        \App\ProductVendorAssignment::create([
            'product_id' => $product->id,
            'vendor_id' => \App\Vendor::factory()->create()->id,
            'assignment_type' => 'cleaning',
            'status' => 'returned',
            'assigned_date' => now()->subHours(6)->toDateString(),
            'actual_return_date' => now()->subHours(2)->toDateString(), // 2 hours ago
            'assigned_by' => $this->user->id,
            'returned_by' => $this->user->id,
        ]);

        $response = $this->getJson('/api/products?condition=needs_attention');

        $response->assertStatus(200);
        
        $productNames = collect($response->json('data'))->pluck('name')->toArray();
        
        // Should NOT include recently returned product
        $this->assertNotContains('Recently Returned Product', $productNames);
    }

    /** @test */
    public function it_includes_products_returned_more_than_4_hours_ago()
    {
        $product = Product::factory()->create(['name' => 'Old Returned Product']);
        
        // Mark as dirty
        ProductMaintenanceService::updateStatusManually($product->id, 'dirty', 'Test', $this->user->id);
        
        // Create an old return record (more than 4 hours ago)
        \App\ProductVendorAssignment::create([
            'product_id' => $product->id,
            'vendor_id' => \App\Vendor::factory()->create()->id,
            'assignment_type' => 'cleaning',
            'status' => 'returned',
            'assigned_date' => now()->subHours(10)->toDateString(),
            'actual_return_date' => now()->subHours(6)->toDateString(), // 6 hours ago
            'assigned_by' => $this->user->id,
            'returned_by' => $this->user->id,
        ]);

        $response = $this->getJson('/api/products?condition=needs_attention');

        $response->assertStatus(200);
        
        $productNames = collect($response->json('data'))->pluck('name')->toArray();
        
        // Should include old returned product that needs attention
        $this->assertContains('Old Returned Product', $productNames);
    }

    /** @test */
    public function it_only_considers_latest_maintenance_status()
    {
        $product = Product::factory()->create(['name' => 'Status History Product']);
        
        // Create multiple status records
        ProductMaintenanceService::updateStatusManually($product->id, 'dirty', 'First status', $this->user->id);
        sleep(1); // Ensure different timestamps
        ProductMaintenanceService::updateStatusManually($product->id, 'under_cleaning', 'Assigned to vendor', $this->user->id);
        sleep(1);
        ProductMaintenanceService::updateStatusManually($product->id, 'clean', 'Returned clean', $this->user->id);

        $response = $this->getJson('/api/products?condition=needs_attention');

        $response->assertStatus(200);
        
        $productNames = collect($response->json('data'))->pluck('name')->toArray();
        
        // Should NOT include product since latest status is 'clean'
        $this->assertNotContains('Status History Product', $productNames);
    }

    /** @test */
    public function it_returns_correct_maintenance_status_structure()
    {
        $product = Product::factory()->create();
        ProductMaintenanceService::updateStatusManually($product->id, 'damaged', 'Test damaged', $this->user->id);

        $response = $this->getJson('/api/products');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'maintenance_status' => [
                                'status',
                                'label',
                                'color',
                                'is_clean',
                                'notes',
                                'last_cleaned_at',
                                'last_maintained_at'
                            ]
                        ]
                    ]
                ]);

        $productData = $response->json('data')[0];
        $this->assertEquals('damaged', $productData['maintenance_status']['status']);
        $this->assertEquals('Damaged', $productData['maintenance_status']['label']);
        $this->assertEquals('red', $productData['maintenance_status']['color']);
    }

    /** @test */
    public function it_requires_authentication()
    {
        // Remove authentication
        auth()->logout();

        $response = $this->getJson('/api/products');

        $response->assertStatus(401);
    }
}
