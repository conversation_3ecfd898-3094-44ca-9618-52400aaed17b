// Test core business logic functions
describe('CRM Business Logic Tests', () => {
  
  // Test product status management
  describe('Product Status Management', () => {
    test('should determine correct status colors', () => {
      const getStatusColor = (status) => {
        const colors = {
          'ready': 'green',
          'needs_cleaning': 'orange', 
          'damaged': 'red',
          'under_cleaning': 'blue',
          'under_repair': 'blue',
          'under_maintenance': 'blue'
        };
        return colors[status] || 'gray';
      };

      expect(getStatusColor('ready')).toBe('green');
      expect(getStatusColor('needs_cleaning')).toBe('orange');
      expect(getStatusColor('damaged')).toBe('red');
      expect(getStatusColor('under_cleaning')).toBe('blue');
      expect(getStatusColor('unknown')).toBe('gray');
    });

    test('should filter products by status correctly', () => {
      const products = [
        { id: 1, status: 'ready', name: 'Product 1' },
        { id: 2, status: 'needs_cleaning', name: 'Product 2' },
        { id: 3, status: 'damaged', name: 'Product 3' },
        { id: 4, status: 'ready', name: 'Product 4' }
      ];

      const filterByStatus = (products, status) => {
        if (status === 'all') return products;
        return products.filter(p => p.status === status);
      };

      const readyProducts = filterByStatus(products, 'ready');
      const needsAttention = filterByStatus(products, 'needs_cleaning');
      const allProducts = filterByStatus(products, 'all');

      expect(readyProducts).toHaveLength(2);
      expect(needsAttention).toHaveLength(1);
      expect(allProducts).toHaveLength(4);
    });

    test('should identify products that need attention', () => {
      const needsAttentionStatuses = ['needs_cleaning', 'needs_maintenance', 'damaged'];
      
      const checkNeedsAttention = (status) => {
        return needsAttentionStatuses.includes(status);
      };

      expect(checkNeedsAttention('needs_cleaning')).toBe(true);
      expect(checkNeedsAttention('damaged')).toBe(true);
      expect(checkNeedsAttention('ready')).toBe(false);
      expect(checkNeedsAttention('under_cleaning')).toBe(false);
    });
  });

  // Test vendor assignment logic
  describe('Vendor Assignment Logic', () => {
    test('should validate vendor types', () => {
      const validVendorTypes = ['dry_cleaner', 'repair_shop', 'tailor', 'alteration_shop'];
      
      const isValidVendorType = (type) => {
        return validVendorTypes.includes(type);
      };

      expect(isValidVendorType('dry_cleaner')).toBe(true);
      expect(isValidVendorType('repair_shop')).toBe(true);
      expect(isValidVendorType('invalid_type')).toBe(false);
    });

    test('should determine assignment type based on vendor and product status', () => {
      const getAssignmentType = (vendorType, productStatus) => {
        if (vendorType === 'dry_cleaner' && productStatus === 'needs_cleaning') {
          return 'cleaning';
        }
        if (vendorType === 'repair_shop' && productStatus === 'damaged') {
          return 'repair';
        }
        if (vendorType === 'tailor') {
          return 'alteration';
        }
        return 'maintenance';
      };

      expect(getAssignmentType('dry_cleaner', 'needs_cleaning')).toBe('cleaning');
      expect(getAssignmentType('repair_shop', 'damaged')).toBe('repair');
      expect(getAssignmentType('tailor', 'any')).toBe('alteration');
    });

    test('should check if product can be assigned to vendor', () => {
      const canAssignToVendor = (product, vendor) => {
        // Product must need attention
        const needsAttention = ['needs_cleaning', 'needs_maintenance', 'damaged'].includes(product.status);
        
        // Product must not be with another vendor
        const notWithVendor = !product.with_vendor;
        
        // Vendor must be active
        const vendorActive = vendor.status === 'active';

        return needsAttention && notWithVendor && vendorActive;
      };

      const product1 = { id: 1, status: 'needs_cleaning', with_vendor: false };
      const product2 = { id: 2, status: 'ready', with_vendor: false };
      const product3 = { id: 3, status: 'needs_cleaning', with_vendor: true };
      
      const activeVendor = { id: 1, status: 'active' };
      const inactiveVendor = { id: 2, status: 'inactive' };

      expect(canAssignToVendor(product1, activeVendor)).toBe(true);
      expect(canAssignToVendor(product2, activeVendor)).toBe(false); // Ready product
      expect(canAssignToVendor(product3, activeVendor)).toBe(false); // Already with vendor
      expect(canAssignToVendor(product1, inactiveVendor)).toBe(false); // Inactive vendor
    });
  });

  // Test search and filtering
  describe('Search and Filtering', () => {
    test('should search products by name', () => {
      const products = [
        { id: 1, name: 'Pink Reception Lehnga' },
        { id: 2, name: 'Blue Wedding Dress' },
        { id: 3, name: 'Red Party Wear' },
        { id: 4, name: 'Pink Saree' }
      ];

      const searchProducts = (products, searchTerm) => {
        if (!searchTerm) return products;
        return products.filter(p => 
          p.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      };

      const pinkProducts = searchProducts(products, 'Pink');
      const weddingProducts = searchProducts(products, 'Wedding');
      const emptySearch = searchProducts(products, '');

      expect(pinkProducts).toHaveLength(2);
      expect(weddingProducts).toHaveLength(1);
      expect(emptySearch).toHaveLength(4);
    });

    test('should search vendors by name and type', () => {
      const vendors = [
        { id: 1, name: 'Premium Dry Cleaners', type: 'dry_cleaner' },
        { id: 2, name: 'Fix It Right Repairs', type: 'repair_shop' },
        { id: 3, name: 'Elite Dry Cleaners', type: 'dry_cleaner' }
      ];

      const searchVendors = (vendors, searchTerm, type) => {
        let filtered = vendors;
        
        if (searchTerm) {
          filtered = filtered.filter(v => 
            v.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (type && type !== 'all') {
          filtered = filtered.filter(v => v.type === type);
        }
        
        return filtered;
      };

      const dryCleaners = searchVendors(vendors, '', 'dry_cleaner');
      const premiumVendors = searchVendors(vendors, 'Premium', '');
      const premiumDryCleaners = searchVendors(vendors, 'Premium', 'dry_cleaner');

      expect(dryCleaners).toHaveLength(2);
      expect(premiumVendors).toHaveLength(1);
      expect(premiumDryCleaners).toHaveLength(1);
    });
  });

  // Test utility functions
  describe('Utility Functions', () => {
    test('should format currency correctly', () => {
      const formatCurrency = (amount) => {
        return `₹${amount.toLocaleString()}`;
      };

      expect(formatCurrency(100)).toBe('₹100');
      expect(formatCurrency(1500)).toBe('₹1,500');
      expect(formatCurrency(50000)).toBe('₹50,000');
    });

    test('should format dates correctly', () => {
      const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
      };

      const testDate = '2025-07-09';
      const formatted = formatDate(testDate);
      
      // Should be in MM/DD/YYYY or DD/MM/YYYY format
      expect(formatted).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });

    test('should calculate days between dates', () => {
      const daysBetween = (date1, date2) => {
        const oneDay = 24 * 60 * 60 * 1000;
        const firstDate = new Date(date1);
        const secondDate = new Date(date2);
        
        return Math.round(Math.abs((firstDate - secondDate) / oneDay));
      };

      expect(daysBetween('2025-07-09', '2025-07-12')).toBe(3);
      expect(daysBetween('2025-07-01', '2025-07-01')).toBe(0);
    });

    test('should validate email format', () => {
      const isValidEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      };

      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
    });

    test('should validate phone number format', () => {
      const isValidPhone = (phone) => {
        // Simple validation for 10 digits
        const phoneRegex = /^\d{10}$/;
        return phoneRegex.test(phone.replace(/\D/g, ''));
      };

      expect(isValidPhone('1234567890')).toBe(true);
      expect(isValidPhone('************')).toBe(true);
      expect(isValidPhone('123')).toBe(false);
      expect(isValidPhone('abcd')).toBe(false);
    });
  });
});
