<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\Models\Order;
use Mr<PERSON><PERSON>em\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Enums\OrderStatus;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $query = Order::with(['product.user.profile', 'user.profile']);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('product', function ($product) use ($search) {
                    $product->where('name', 'like', "%{$search}%");
                })->orWhereHas('user', function ($user) use ($search) {
                    $user->where('name', 'like', "%{$search}%")
                         ->orWhere('email', 'like', "%{$search}%");
                });
            });
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by purpose
        if ($request->has('purpose')) {
            $query->where('purpose', $request->purpose);
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('from', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('to', '<=', $request->to_date);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $orders = $query->paginate($request->get('per_page', 15));

        return response()->json($orders);
    }

    public function show(Order $order)
    {
        return response()->json([
            'order' => $order->load([
                'product.user.profile',
                'product.product_image',
                'product.productAccessories',
                'user.profile'
            ])
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'user_id' => 'required|exists:users,id',
            'purpose' => 'required|in:rent,sell',
            'from' => 'required|date|after_or_equal:today',
            'to' => 'required|date|after:from',
            'is_clean' => 'boolean',
            'has_cover' => 'boolean',
            'has_hanger' => 'boolean',
            'security_deposit_cost' => 'nullable|numeric|min:0',
            'accessories' => 'nullable|array',
            'rental_cost' => 'required|numeric|min:0',
            'discount_cost' => 'nullable|numeric|min:0|lt:rental_cost',
            'status' => 'nullable|in:' . implode(',', OrderStatus::values()),
            'remark' => 'nullable|string|max:500',
        ]);

        // Additional business logic validation
        $this->validateOrderBusinessRules($request);

        $order = Order::create($request->all());

        return response()->json([
            'order' => $order->load(['product.user.profile', 'product.product_image', 'user.profile']),
            'message' => 'Order created successfully'
        ], 201);
    }

    /**
     * Validate business rules for order creation
     */
    private function validateOrderBusinessRules(Request $request)
    {
        $product = Product::with(['latestMaintenanceStatus', 'activeVendorAssignment.vendor'])
                          ->findOrFail($request->product_id);

        $fromDate = Carbon::parse($request->from);
        $toDate = Carbon::parse($request->to);
        $today = Carbon::today();

        // 1. Check rental period limits
        $rentalDays = $fromDate->diffInDays($toDate);
        if ($rentalDays > 30) {
            throw ValidationException::withMessages([
                'to' => ['Maximum rental period is 30 days.']
            ]);
        }

        // 2. Check if product is available (not with vendor during requested period)
        $vendorAssignment = $product->activeVendorAssignment;
        if ($vendorAssignment && $vendorAssignment->expected_return_date) {
            $expectedReturnDate = Carbon::parse($vendorAssignment->expected_return_date);

            if ($expectedReturnDate->gte($fromDate)) {
                throw ValidationException::withMessages([
                    'from' => [
                        "Product is currently with {$vendorAssignment->vendor->name} for {$vendorAssignment->assignment_type}. " .
                        "Expected return: {$expectedReturnDate->format('M d, Y')}. " .
                        "Please select a start date after the return date."
                    ]
                ]);
            }
        }

        // 3. Check product maintenance status
        $maintenanceStatus = $product->latestMaintenanceStatus;
        if ($maintenanceStatus) {
            $unavailableStatuses = ['needs_cleaning', 'needs_maintenance', 'under_cleaning', 'under_alteration', 'under_maintenance'];

            if (in_array($maintenanceStatus->maintenance_status, $unavailableStatuses)) {
                $statusLabels = [
                    'needs_cleaning' => 'needs cleaning',
                    'needs_maintenance' => 'needs maintenance',
                    'under_cleaning' => 'is currently being cleaned',
                    'under_alteration' => 'is currently being altered',
                    'under_maintenance' => 'is currently under maintenance'
                ];

                throw ValidationException::withMessages([
                    'product_id' => [
                        "This product {$statusLabels[$maintenanceStatus->maintenance_status]} and is not available for rent at this time."
                    ]
                ]);
            }
        }

        // 4. Check for overlapping orders
        $overlappingOrders = Order::where('product_id', $request->product_id)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('from', [$fromDate, $toDate])
                      ->orWhereBetween('to', [$fromDate, $toDate])
                      ->orWhere(function ($q) use ($fromDate, $toDate) {
                          $q->where('from', '<=', $fromDate)
                            ->where('to', '>=', $toDate);
                      });
            })
            ->exists();

        if ($overlappingOrders) {
            throw ValidationException::withMessages([
                'from' => ['This product is already booked for the selected dates.']
            ]);
        }

        // 5. Validate discount doesn't exceed rental cost
        $discountCost = $request->discount_cost ?? 0;
        $rentalCost = $request->rental_cost;

        if ($discountCost >= $rentalCost) {
            throw ValidationException::withMessages([
                'discount_cost' => ['Discount cannot be equal to or greater than rental cost.']
            ]);
        }
    }

    public function update(Request $request, Order $order)
    {
        $request->validate([
            'purpose' => 'required|in:rent,sell',
            'from' => 'required|date',
            'to' => 'required|date|after:from',
            'is_clean' => 'boolean',
            'has_cover' => 'boolean',
            'has_hanger' => 'boolean',
            'security_deposit_cost' => 'nullable|numeric|min:0',
            'accessories' => 'nullable|array',
            'rental_cost' => 'required|numeric|min:0',
            'discount_cost' => 'nullable|numeric|min:0',
            'status' => 'required|in:' . implode(',', OrderStatus::values()),
        ]);

        $order->update($request->all());

        return response()->json([
            'order' => $order->load(['product.user.profile', 'product.product_image', 'user.profile']),
            'message' => 'Order updated successfully'
        ]);
    }

    public function destroy(Order $order)
    {
        $order->delete();

        return response()->json([
            'message' => 'Order deleted successfully'
        ]);
    }

    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,picked_up,delivered,returned,cancelled',
        ]);

        $order->update(['status' => $request->status]);

        return response()->json([
            'order' => $order->load(['product.user.profile', 'product.product_image', 'user.profile']),
            'message' => 'Order status updated successfully'
        ]);
    }
}
