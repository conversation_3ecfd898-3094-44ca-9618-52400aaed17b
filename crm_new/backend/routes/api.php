<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\ColorController;
use App\Http\Controllers\Api\FeatureController;
use App\Http\Controllers\Api\MasterCityController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\OrderReturnController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ProductImageController;
use App\Http\Controllers\Api\ProductAccessoryController;
use App\Http\Controllers\Api\AccessoryImageController;
use App\Http\Controllers\Api\ProductTimelineController;
use App\Http\Controllers\Api\StatusController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VendorController;
use App\Http\Controllers\Api\BusinessContactController;
use App\Http\Controllers\Api\TempImageController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use MrBohem\LaradrobeServices\ProductMaintenanceService;

// Public routes
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/me', [AuthController::class, 'me']);

    // User management
    Route::apiResource('users', UserController::class);

    // Product management
    Route::apiResource('products', ProductController::class);
    Route::post('products/{id}/restore', [ProductController::class, 'restore']);
    Route::delete('products/{id}/force-delete', [ProductController::class, 'forceDelete']);
    Route::patch('products/{id}/maintenance-status', [ProductController::class, 'updateMaintenanceStatus']);

    // Product Timeline
    Route::get('products/{productId}/timeline', [ProductTimelineController::class, 'getTimeline']);
    Route::get('products/{productId}/timeline/{type}', [ProductTimelineController::class, 'getTimelineByType']);
    Route::get('products/{productId}/timeline-summary', [ProductTimelineController::class, 'getTimelineSummary']);

    // Product Images (nested resource)
    Route::apiResource('products.images', ProductImageController::class)->except(['create', 'edit']);
    Route::post('products/{product}/images/{image}/set-thumbnail', [ProductImageController::class, 'setAsThumbnail']);
    // Temp Images Management
    Route::get('temp-images', [TempImageController::class, 'index']);
    Route::delete('temp-images/{filename}', [TempImageController::class, 'destroy']);
    Route::post('temp-images/bulk-delete', [TempImageController::class, 'bulkDelete']);
    Route::post('temp-images/remove-background', [TempImageController::class, 'removeBackground']);
    Route::get('temp-images/stats', [TempImageController::class, 'getStats']);
    Route::post('temp-images/cleanup', [TempImageController::class, 'cleanup']);
    Route::get('temp-images/search', [TempImageController::class, 'search']);

    // Product Accessories (nested resource)
    Route::apiResource('products.accessories', ProductAccessoryController::class)->except(['create', 'edit']);

    // Accessory Images (nested resource)
    Route::get('products/{product}/accessories/{accessory}/images', [AccessoryImageController::class, 'index']);
    Route::post('products/{product}/accessories/{accessory}/images', [AccessoryImageController::class, 'store']);
    Route::delete('products/{product}/accessories/{accessory}/images/{image}', [AccessoryImageController::class, 'destroy']);

    // Category management
    Route::apiResource('categories', CategoryController::class);
    Route::post('categories/{id}/restore', [CategoryController::class, 'restore']);
    Route::get('categories-list', [CategoryController::class, 'getCategories']);

    // Color management
    Route::get('colors', [ColorController::class, 'index']);

    // Feature management
    Route::get('sizes', [FeatureController::class, 'getSizes']);
    Route::get('features', [FeatureController::class, 'getFeatures']);

    // Master City management
    Route::apiResource('master-cities', MasterCityController::class);
    Route::post('master-cities/{id}/restore', [MasterCityController::class, 'restore']);

    // Order management
    Route::apiResource('orders', OrderController::class);
    Route::patch('orders/{order}/status', [OrderController::class, 'updateStatus']);

    // Order Return management
    Route::apiResource('order-returns', OrderReturnController::class);
    Route::patch('order-returns/{orderReturn}/status', [OrderReturnController::class, 'updateStatus']);
    Route::get('orders/{order}/returns', [OrderReturnController::class, 'getOrderReturns']);

    // Vendor management
    Route::apiResource('vendors', VendorController::class);
    Route::post('vendors/assign-product', [VendorController::class, 'assignProduct']);
    Route::patch('vendor-assignments/{assignment}/return', [VendorController::class, 'markProductReturned']);
    Route::get('vendor-dashboard', [VendorController::class, 'dashboard']);
    Route::get('products-with-vendors', [VendorController::class, 'productsWithVendors']);
    Route::get('overdue-assignments', [VendorController::class, 'overdueAssignments']);



    // Status Management
    Route::prefix('statuses')->group(function () {
        Route::get('/products', [StatusController::class, 'getProductStatuses']);
        Route::get('/products/maintenance-options', [StatusController::class, 'getMaintenanceStatusOptions']);
        Route::get('/orders', [StatusController::class, 'getOrderStatuses']);
        Route::get('/vendor-assignments', [StatusController::class, 'getVendorAssignmentStatuses']);
        Route::get('/products/counts', [StatusController::class, 'getProductStatusCounts']);
        Route::get('/orders/counts', [StatusController::class, 'getOrderStatusCounts']);
        Route::get('/vendor-assignments/counts', [StatusController::class, 'getVendorAssignmentStatusCounts']);
        Route::get('/overview', [StatusController::class, 'getStatusOverview']);
        Route::post('/validate-transition', [StatusController::class, 'validateStatusTransition']);
    });

    // Dashboard stats
    Route::get('/dashboard/stats', function () {
        return response()->json([
            'total_users' => \App\Models\User::count(),
            'total_products' => \App\Models\Product::count(),
            'total_orders' => \App\Models\Order::count(),
            'pending_orders' => \App\Models\Order::where('status', 'pending')->count(),
            'confirmed_orders' => \App\Models\Order::where('status', 'confirmed')->count(),
            'delivered_orders' => \App\Models\Order::where('status', 'delivered')->count(),
        ]);
    });

    // Business Contacts management
    Route::apiResource('business-contacts', BusinessContactController::class);
    Route::post('business-contacts/{id}/restore', [BusinessContactController::class, 'restore']);
    Route::patch('business-contacts/bulk-update-status', [BusinessContactController::class, 'bulkUpdateStatus']);
    Route::get('business-contacts-options/interest-status', [BusinessContactController::class, 'getInterestStatusOptions']);
    Route::get('business-contacts-options/lead-source', [BusinessContactController::class, 'getLeadSourceOptions']);
    Route::get('business-contacts-statistics', [BusinessContactController::class, 'getStatistics']);
});
