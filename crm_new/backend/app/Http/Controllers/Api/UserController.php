<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::with(['profile', 'products']);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('profile', function ($profile) use ($search) {
                      $profile->where('phone', 'like', "%{$search}%")
                              ->orWhere('city', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by admin status (based on email)
        if ($request->has('role')) {
            if ($request->role === 'admin') {
                $query->where('email', 'like', '%<EMAIL>');
            } else {
                $query->where('email', 'not like', '%<EMAIL>');
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $users = $query->paginate($request->get('per_page', 15));

        return response()->json($users);
    }

    public function show(User $user)
    {
        return response()->json([
            'user' => $user->load(['profile', 'products.images', 'orders.product'])
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:15',
            'city' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:500',
            'age' => 'nullable|integer|min:1|max:120',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        // Create profile if profile data is provided
        if ($request->has(['phone', 'city', 'address', 'age'])) {
            $user->profile()->create([
                'phone' => $request->phone,
                'city' => $request->city,
                'address' => $request->address,
                'age' => $request->age,
            ]);
        }

        return response()->json([
            'user' => $user->load('profile'),
            'message' => 'User created successfully'
        ], 201);
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:15',
            'city' => 'nullable|string|max:50',
            'address' => 'nullable|string|max:500',
            'age' => 'nullable|integer|min:1|max:120',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        // Update or create profile
        $user->profile()->updateOrCreate(
            ['user_id' => $user->id],
            [
                'phone' => $request->phone,
                'city' => $request->city,
                'address' => $request->address,
                'age' => $request->age,
            ]
        );

        return response()->json([
            'user' => $user->load('profile'),
            'message' => 'User updated successfully'
        ]);
    }

    public function destroy(User $user)
    {
        try {
            // Start a database transaction for data integrity
            DB::beginTransaction();

            // Delete the user's profile first (if exists)
            if ($user->profile) {
                $user->profile->delete();
            }

            // Handle related orders - soft delete them to preserve order history
            if (method_exists($user, 'orders')) {
                $user->orders()->delete(); // This will soft delete if the Order model uses SoftDeletes
            }

            // Handle related products - you might want to keep them or transfer ownership
            // Option 1: Delete products (uncomment if you want to delete)
            // $user->products()->delete();

            // Option 2: Set products to null user_id (orphan them)
            $user->products()->update(['user_id' => null]);

            // Handle conversations - detach from pivot table
            if (method_exists($user, 'conversations')) {
                $user->conversations()->detach();
            }

            // Handle messages - use direct DB query since the relationship might have wrong foreign key
            DB::table('messages')->where('user_id', $user->id)->delete();

            // Handle cart items - delete user's cart items
            DB::table('carts')->where('user_id', $user->id)->delete();

            // Handle referrals - you might want to keep referral data
            // Just nullify the foreign keys instead of deleting
            if (Schema::hasTable('referrals')) {
                DB::table('referrals')->where('referee_id', $user->id)->update(['referee_id' => null]);
                DB::table('referrals')->where('referrer_id', $user->id)->update(['referrer_id' => null]);
            }

            // Finally delete the user
            $user->delete();

            // Commit the transaction
            DB::commit();

            return response()->json([
                'message' => 'User deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollback();

            return response()->json([
                'message' => 'Failed to delete user: ' . $e->getMessage()
            ], 500);
        }
    }
}
