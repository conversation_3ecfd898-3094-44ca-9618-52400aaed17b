<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\Models\BusinessContact;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;

class BusinessContactController extends Controller
{
    /**
     * Display a listing of business contacts
     */
    public function index(Request $request)
    {
        $query = BusinessContact::query();

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('business_name', 'like', "%{$search}%")
                  ->orWhere('business_owner_name', 'like', "%{$search}%")
                  ->orWhere('business_phone', 'like', "%{$search}%")
                  ->orWhere('business_address', 'like', "%{$search}%");
            });
        }

        // Filter by interest status
        if ($request->has('interest_status') && !empty($request->interest_status)) {
            $query->where('interest_status', $request->interest_status);
        }

        // Filter by lead source
        if ($request->has('lead_source') && !empty($request->lead_source)) {
            $query->where('lead_source', $request->lead_source);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSortFields = ['business_name', 'business_owner_name', 'interest_status', 'lead_source', 'created_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $contacts = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $contacts->items(),
            'pagination' => [
                'current_page' => $contacts->currentPage(),
                'last_page' => $contacts->lastPage(),
                'per_page' => $contacts->perPage(),
                'total' => $contacts->total(),
                'from' => $contacts->firstItem(),
                'to' => $contacts->lastItem(),
            ]
        ]);
    }

    /**
     * Store a newly created business contact
     */
    public function store(Request $request)
    {
        $request->validate([
            'business_name' => 'required|string|max:255',
            'business_phone' => 'nullable|string|max:20',
            'business_owner_name' => 'nullable|string|max:255',
            'business_address' => 'nullable|string|max:500',
            'lead_source' => 'nullable|in:' . implode(',', array_keys(BusinessContact::getLeadSourceOptions())),
            'notes' => 'nullable|string|max:1000',
            'interest_status' => 'nullable|in:' . implode(',', array_keys(BusinessContact::getInterestStatusOptions())),
        ]);

        $contact = BusinessContact::create($request->all());

        return response()->json([
            'success' => true,
            'data' => $contact,
            'message' => 'Business contact created successfully'
        ], 201);
    }

    /**
     * Display the specified business contact
     */
    public function show($id)
    {
        $contact = BusinessContact::findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $contact
        ]);
    }

    /**
     * Update the specified business contact
     */
    public function update(Request $request, $id)
    {
        $contact = BusinessContact::findOrFail($id);

        $request->validate([
            'business_name' => 'required|string|max:255',
            'business_phone' => 'nullable|string|max:20',
            'business_owner_name' => 'nullable|string|max:255',
            'business_address' => 'nullable|string|max:500',
            'lead_source' => 'nullable|in:' . implode(',', array_keys(BusinessContact::getLeadSourceOptions())),
            'notes' => 'nullable|string|max:1000',
            'interest_status' => 'nullable|in:' . implode(',', array_keys(BusinessContact::getInterestStatusOptions())),
        ]);

        $contact->update($request->all());

        return response()->json([
            'success' => true,
            'data' => $contact,
            'message' => 'Business contact updated successfully'
        ]);
    }

    /**
     * Remove the specified business contact (soft delete)
     */
    public function destroy($id)
    {
        $contact = BusinessContact::findOrFail($id);
        $contact->delete();

        return response()->json([
            'success' => true,
            'message' => 'Business contact deleted successfully'
        ]);
    }

    /**
     * Restore a soft deleted business contact
     */
    public function restore($id)
    {
        $contact = BusinessContact::withTrashed()->findOrFail($id);
        $contact->restore();

        return response()->json([
            'success' => true,
            'data' => $contact,
            'message' => 'Business contact restored successfully'
        ]);
    }

    /**
     * Get interest status options
     */
    public function getInterestStatusOptions()
    {
        return response()->json([
            'success' => true,
            'data' => BusinessContact::getInterestStatusOptions()
        ]);
    }

    /**
     * Get lead source options
     */
    public function getLeadSourceOptions()
    {
        return response()->json([
            'success' => true,
            'data' => BusinessContact::getLeadSourceOptions()
        ]);
    }

    /**
     * Bulk update interest status
     */
    public function bulkUpdateStatus(Request $request)
    {
        $request->validate([
            'contact_ids' => 'required|array',
            'contact_ids.*' => 'exists:business_contacts,id',
            'interest_status' => 'required|in:' . implode(',', array_keys(BusinessContact::getInterestStatusOptions())),
        ]);

        BusinessContact::whereIn('id', $request->contact_ids)
                      ->update(['interest_status' => $request->interest_status]);

        return response()->json([
            'success' => true,
            'message' => 'Business contacts updated successfully'
        ]);
    }

    /**
     * Get dashboard statistics
     */
    public function getStatistics()
    {
        $stats = [
            'total_contacts' => BusinessContact::count(),
            'interested' => BusinessContact::where('interest_status', 'interested')->count(),
            'converted' => BusinessContact::where('interest_status', 'converted')->count(),
            'follow_up' => BusinessContact::where('interest_status', 'follow_up')->count(),
            'not_contacted' => BusinessContact::where('interest_status', 'not contacted')->count(),
            'by_lead_source' => BusinessContact::selectRaw('lead_source, COUNT(*) as count')
                                              ->groupBy('lead_source')
                                              ->pluck('count', 'lead_source')
                                              ->toArray(),
            'recent_contacts' => BusinessContact::latest()->take(5)->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
