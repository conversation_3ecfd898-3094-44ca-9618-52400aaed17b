<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasUuids,SoftDeletes;

    protected $fillable = [
        'user_id',
        'product_id',
        'purpose',
        'from',
        'to',
        'is_clean',
        'has_cover',
        'has_hanger',
        'security_deposit_cost',
        'accessories',
        'rental_cost',
        'discount_cost',
    ];

    protected $casts = [
        'accessories' => 'array',
        'from' => 'date',
        'to' => 'date',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
