import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_layout.dart';
import '../providers/data_provider.dart';
import '../models/vendor.dart';
import '../utils/app_theme.dart';

class VendorsScreen extends StatefulWidget {
  const VendorsScreen({super.key});

  @override
  State<VendorsScreen> createState() => _VendorsScreenState();
}

class _VendorsScreenState extends State<VendorsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadVendors();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadVendors() async {
    final dataProvider = Provider.of<DataProvider>(context, listen: false);
    await dataProvider.fetchVendors();
  }

  @override
  Widget build(BuildContext context) {
    return AppLayout(
      title: 'Vendors',
      currentRoute: '/vendors',
      child: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search vendors...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value.toLowerCase();
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                IconButton(
                  onPressed: _loadVendors,
                  icon: const Icon(Icons.refresh),
                  style: IconButton.styleFrom(
                    backgroundColor: AppTheme.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Vendors List
          Expanded(
            child: Consumer<DataProvider>(
              builder: (context, dataProvider, child) {
                if (dataProvider.vendorsLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (dataProvider.vendorsError != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: AppTheme.error),
                        const SizedBox(height: 16),
                        Text('Error loading vendors', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppTheme.gray900)),
                        const SizedBox(height: 8),
                        Text(dataProvider.vendorsError!, style: TextStyle(color: AppTheme.gray600), textAlign: TextAlign.center),
                        const SizedBox(height: 16),
                        ElevatedButton(onPressed: _loadVendors, child: const Text('Retry')),
                      ],
                    ),
                  );
                }

                final vendors = dataProvider.vendors.where((vendor) {
                  if (_searchQuery.isEmpty) return true;
                  return vendor.name.toLowerCase().contains(_searchQuery) ||
                         (vendor.type?.toLowerCase().contains(_searchQuery) ?? false);
                }).toList();

                if (vendors.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.business_outlined, size: 64, color: AppTheme.gray400),
                        const SizedBox(height: 16),
                        Text(_searchQuery.isEmpty ? 'No vendors found' : 'No vendors match your search', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: AppTheme.gray900)),
                        const SizedBox(height: 8),
                        Text(_searchQuery.isEmpty ? 'Vendors will appear here' : 'Try adjusting your search terms', style: TextStyle(color: AppTheme.gray600)),
                      ],
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: _loadVendors,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: vendors.length,
                    itemBuilder: (context, index) {
                      final vendor = vendors[index];
                      return _buildVendorCard(vendor);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVendorCard(Vendor vendor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(vendor.name, style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16)),
                ),
                if (vendor.type != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      vendor.typeLabel,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primary,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            if (vendor.contactPerson != null) Text('Contact: ${vendor.contactPerson!}', style: const TextStyle(color: AppTheme.gray600)),
            if (vendor.phone != null) Text('Phone: ${vendor.phone!}', style: const TextStyle(color: AppTheme.gray600)),
            if (vendor.email != null) Text('Email: ${vendor.email!}', style: const TextStyle(color: AppTheme.gray600)),
            if (vendor.fullAddress.isNotEmpty) Text('Address: ${vendor.fullAddress}', style: const TextStyle(color: AppTheme.gray600)),
            if (vendor.ratePerItem != null) Text('Rate: ${vendor.formattedRate}', style: const TextStyle(color: AppTheme.gray600)),
            if (vendor.assignmentsCount != null) Text('Total Assignments: ${vendor.assignmentsCount}', style: const TextStyle(color: AppTheme.gray600)),
            if (vendor.notes != null) ...[
              const SizedBox(height: 8),
              Text(vendor.notes!, style: const TextStyle(color: AppTheme.gray700, fontSize: 14)),
            ],
          ],
        ),
      ),
    );
  }
}
