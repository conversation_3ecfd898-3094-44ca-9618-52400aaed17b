<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use MrBohem\LaradrobeServices\Models\OrderReturn;
use MrBohem\LaradrobeServices\Models\Order;
use MrBohem\LaradrobeServices\ProductMaintenanceService;

class OrderReturnController extends Controller
{
    public function index(Request $request)
    {
        $query = OrderReturn::with(['order.product', 'order.user.profile', 'returnedBy.profile']);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('order.product', function ($product) use ($search) {
                    $product->where('name', 'like', "%{$search}%");
                })->orWhereHas('order.user', function ($user) use ($search) {
                    $user->where('name', 'like', "%{$search}%")
                         ->orWhere('email', 'like', "%{$search}%");
                });
            });
        }

        // Filter by return status
        if ($request->has('status')) {
            $query->where('return_status', $request->status);
        }

        // Filter by condition
        if ($request->has('condition')) {
            $query->where('product_condition', $request->condition);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'returned_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $returns = $query->paginate($request->get('per_page', 15));

        return response()->json($returns);
    }

    public function show(OrderReturn $orderReturn)
    {
        return response()->json([
            'return' => $orderReturn->load([
                'order.product.user.profile',
                'order.product.images',
                'order.product.accessories',
                'order.user.profile',
                'returnedBy.profile'
            ])
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'order_id' => 'required|exists:orders,id',
            'product_condition' => 'required|in:excellent,good,fair,poor,damaged',
            'product_is_clean' => 'boolean',
            'product_notes' => 'nullable|string|max:1000',
            'cover_returned' => 'boolean',
            'cover_condition' => 'nullable|in:excellent,good,fair,poor,damaged',
            'cover_is_clean' => 'nullable|boolean',
            'cover_notes' => 'nullable|string|max:500',
            'hanger_returned' => 'boolean',
            'hanger_condition' => 'nullable|in:excellent,good,fair,poor,damaged',
            'hanger_notes' => 'nullable|string|max:500',
            'accessories_returned' => 'nullable|array',
            'accessories_condition' => 'nullable|array',
            'accessories_notes' => 'nullable|string|max:500',
            'security_deposit_returned' => 'required|numeric|min:0',
            'damage_charges' => 'nullable|numeric|min:0',
            'cleaning_charges' => 'nullable|numeric|min:0',
            'general_notes' => 'nullable|string|max:1000',
            'return_status' => 'nullable|in:pending,completed,disputed',
        ]);

        DB::beginTransaction();
        try {
            // Create the return record
            $orderReturn = OrderReturn::create([
                ...$validated,
                'returned_by' => auth()->id(),
                'returned_at' => now(),
            ]);

            // Update order status to returned
            $order = Order::find($request->order_id);
            $order->update(['status' => 'returned']);

            // Update product maintenance status based on return condition
            try {
                ProductMaintenanceService::updateStatusFromReturn($orderReturn);
                \Log::info('Product maintenance status updated from return', [
                    'order_return_id' => $orderReturn->id,
                    'product_id' => $order->product_id
                ]);
            } catch (\Exception $e) {
                \Log::error('Failed to update maintenance status from return', [
                    'order_return_id' => $orderReturn->id,
                    'error' => $e->getMessage()
                ]);
                // Don't fail the return process if maintenance update fails
            }

            DB::commit();

            return response()->json([
                'return' => $orderReturn->load([
                    'order.product.user.profile',
                    'order.user.profile',
                    'returnedBy.profile'
                ]),
                'message' => 'Return processed successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to process return: ' . $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, OrderReturn $orderReturn)
    {
        $request->validate([
            'product_condition' => 'required|in:excellent,good,fair,poor,damaged',
            'product_is_clean' => 'boolean',
            'product_notes' => 'nullable|string|max:1000',
            'cover_returned' => 'boolean',
            'cover_condition' => 'nullable|in:excellent,good,fair,poor,damaged',
            'cover_is_clean' => 'nullable|boolean',
            'cover_notes' => 'nullable|string|max:500',
            'hanger_returned' => 'boolean',
            'hanger_condition' => 'nullable|in:excellent,good,fair,poor,damaged',
            'hanger_notes' => 'nullable|string|max:500',
            'accessories_returned' => 'nullable|array',
            'accessories_condition' => 'nullable|array',
            'accessories_notes' => 'nullable|string|max:500',
            'security_deposit_returned' => 'required|numeric|min:0',
            'damage_charges' => 'nullable|numeric|min:0',
            'cleaning_charges' => 'nullable|numeric|min:0',
            'general_notes' => 'nullable|string|max:1000',
            'return_status' => 'required|in:pending,completed,disputed',
        ]);

        $orderReturn->update($request->all());

        return response()->json([
            'return' => $orderReturn->load([
                'order.product.user.profile',
                'order.user.profile',
                'returnedBy.profile'
            ]),
            'message' => 'Return updated successfully'
        ]);
    }

    public function destroy(OrderReturn $orderReturn)
    {
        $orderReturn->delete();

        return response()->json([
            'message' => 'Return record deleted successfully'
        ]);
    }

    // Get returns for a specific order
    public function getOrderReturns(Order $order)
    {
        $returns = $order->returns()->with(['returnedBy.profile'])->get();

        return response()->json([
            'returns' => $returns
        ]);
    }

    // Update return status
    public function updateStatus(Request $request, OrderReturn $orderReturn)
    {
        $request->validate([
            'return_status' => 'required|in:pending,completed,disputed',
        ]);

        $orderReturn->update(['return_status' => $request->return_status]);

        return response()->json([
            'return' => $orderReturn->load([
                'order.product.user.profile',
                'order.user.profile',
                'returnedBy.profile'
            ]),
            'message' => 'Return status updated successfully'
        ]);
    }
}
