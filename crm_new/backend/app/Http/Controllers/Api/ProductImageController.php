<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use MrBohem\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\ProductImage;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;

class ProductImageController extends Controller
{
    public function index(Product $product)
    {
        $images = $product->product_image()->orderBy('created_at', 'desc')->get();

        return response()->json([
            'images' => $images
        ]);
    }

    public function store(Request $request, Product $product)
    {
        // Validate based on upload type
        if ($request->has('upload_type') && $request->upload_type === 'temp') {
            // Validate for temp image selection
            $request->validate([
                'temp_images' => 'required|array|max:10',
                'temp_images.*' => 'required|string', // Image names from temp folder
                'upload_type' => 'required|string|in:temp,file'
            ]);
        } else {
            // Validate for file upload
            $request->validate([
                'images' => 'required|array|max:10',
                'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
                'upload_type' => 'sometimes|string|in:temp,file'
            ]);
        }

        $uploadedImages = [];

        try {
            if ($request->has('upload_type') && $request->upload_type === 'temp') {
                // Handle temp image attachment
                foreach ($request->temp_images as $tempImageName) {
                    // Attach image from temp folder using LaradrobeServices
                    $uploadResult = LaradrobeServices::attachImageFromTemp('TempImage/'.$tempImageName);

                    if ($uploadResult) {
                        // Create database record
                        $productImage = ProductImage::create([
                            'product_id' => $product->id,
                            'user_id' => $product->user_id,
                            'image_name' => $uploadResult, // This contains the S3 filename
                        ]);

                        $uploadedImages[] = $productImage;
                    }
                }
            } else {
                // Handle file upload (existing functionality)
                foreach ($request->file('images') as $image) {
                    // Upload image to S3 using LaradrobeServices
                    $uploadResult = LaradrobeServices::uploadProductImage($image);

                    if ($uploadResult) {
                        // Create database record
                        $productImage = ProductImage::create([
                            'product_id' => $product->id,
                            'user_id' => $product->user_id,
                            'image_name' => $uploadResult, // This contains the S3 filename
                        ]);

                        $uploadedImages[] = $productImage;
                    }
                }
            }

            $uploadType = $request->upload_type === 'temp' ? 'attached from temp folder' : 'uploaded';

            return response()->json([
                'message' => "Images {$uploadType} successfully",
                'images' => $uploadedImages,
                'upload_type' => $request->upload_type ?? 'file'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to process images: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Product $product, ProductImage $image)
    {
        // Ensure the image belongs to the product
        if ($image->product_id !== $product->id) {
            return response()->json([
                'message' => 'Image not found for this product'
            ], 404);
        }

        return response()->json([
            'image' => $image
        ]);
    }

    public function update(Request $request, Product $product, ProductImage $image)
    {
        // Ensure the image belongs to the product
        if ($image->product_id !== $product->id) {
            return response()->json([
                'message' => 'Image not found for this product'
            ], 404);
        }

        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        try {
            // Note: We don't delete the old S3 image here to avoid breaking existing references
            // In production, you might want to implement a cleanup job

            // Upload new image to S3
            $uploadResult = LaradrobeServices::uploadProductImage($request->file('image'));

            if ($uploadResult) {
                // Update database record
                $image->update([
                    'image_name' => $uploadResult,
                    'thumbnail' => $uploadResult,
                ]);

                return response()->json([
                    'message' => 'Image updated successfully',
                    'image' => $image
                ]);
            } else {
                throw new \Exception('Failed to upload image to S3');
            }

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update image: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Product $product, ProductImage $image)
    {
        // Ensure the image belongs to the product
        if ($image->product_id !== $product->id) {
            return response()->json([
                'message' => 'Image not found for this product'
            ], 404);
        }

        try {
            // Note: We don't delete from S3 here to avoid breaking existing references
            // In production, you might want to implement a cleanup job that runs periodically
            // to remove orphaned S3 files

            // Delete database record
            $image->delete();

            return response()->json([
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }

    public function setAsThumbnail(Request $request, Product $product, ProductImage $image)
    {
        // Ensure the image belongs to the product
        if ($image->product_id !== $product->id) {
            return response()->json([
                'message' => 'Image not found for this product'
            ], 404);
        }

        try {
            // Update product thumbnail
            $product->update([
                'thumbnail' => $image->image_name
            ]);

            return response()->json([
                'message' => 'Image set as thumbnail successfully',
                'product' => $product->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to set thumbnail: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available temp images from S3 TempImage folder
     */
    public function getTempImages()
    {
        try {
            // Get files from S3 TempImage folder
            $s3Files = Storage::disk('s3')->files('TempImage');

            $tempImages = [];
            foreach ($s3Files as $file) {
                // Extract filename from path
                $filename = basename($file);

                // Skip if not an image file
                $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    continue;
                }

                $tempImages[] = [
                    'filename' => $filename,
                    'path' => $file,
                    'url' => 'https://laraket.s3.ap-south-1.amazonaws.com/' . $file,
                    'size' => Storage::disk('s3')->size($file),
                    'last_modified' => Storage::disk('s3')->lastModified($file)
                ];
            }

            // Sort by last modified (newest first)
            usort($tempImages, function($a, $b) {
                return $b['last_modified'] - $a['last_modified'];
            });

            return response()->json([
                'temp_images' => $tempImages,
                'count' => count($tempImages)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch temp images: ' . $e->getMessage(),
                'temp_images' => []
            ], 500);
        }
    }
}
