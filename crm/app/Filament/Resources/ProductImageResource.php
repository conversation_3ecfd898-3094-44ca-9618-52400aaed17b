<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductImageResource\Pages;
use App\Filament\Resources\ProductImageResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Guava\FilamentNestedResources\Ancestor;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;
use MrBohem\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\ProductImage;
use Storage;

class ProductImageResource extends Resource
{
    protected static ?string $model = ProductImage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getBreadcrumbs($record,$a){
        return [$record->id];
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('image_name')
                    ->label('Upload Image')
                    ->image()
                    ->disk('s3')
                    ->getUploadedFileNameForStorageUsing(function ($file) {
                        // dd($file);
                        return LaradrobeServices::uploadProductImage($file);
                    }),
                
                Forms\Components\Hidden::make('user_id')
                    ->default( function() {
                        $productId = collect(explode('/', url()->current()))->get(5);
                        $product = Product::where('id', $productId)->first();
                        return $product->user_id;
                    }),
    
                Forms\Components\Hidden::make('product_id')
                    ->default(collect(explode('/', url()->current()))->get(5)),

                Forms\Components\Select::make('image_name1')
                    ->label('Select Image')
                    ->options(function () {
                        $files = Storage::disk('s3')->files('TempImage');
                        $imageFiles = array_filter($files, function ($file) {
                            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);
                        });

                        return array_combine($imageFiles, array_map(function ($file) {
                            return pathinfo($file, PATHINFO_FILENAME); // Display only the filename in the select box
                        }, $imageFiles));
                    })
                    ->searchable() // Optional: Add search functionality if you have many images
                    ->afterStateUpdated(function (?string $state, callable $set, callable $get) {
                        if ($state == null) {
                            return;
                        }
                        $fileName = LaradrobeServices::attachImageFromTemp($state); // Modify your service to handle S3 paths
                        $tempArr = [$fileName => $fileName];
                        $set('image_name', $tempArr);
                    })
                    ->getSearchResultsUsing(function (string $search)  { // Corrected line: Added 'use ($folderPath)'
                        $files = Storage::disk('s3')->files('TempImage');
                        $imageFiles = array_filter($files, function ($file) use ($search) { // Corrected line: Added 'use ($search)'
                            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']) &&
                                   str_contains(pathinfo($file, PATHINFO_FILENAME), $search);
                        });
                
                        return array_combine($imageFiles, array_map(function ($file) {
                            return pathinfo($file, PATHINFO_FILENAME);
                        }, $imageFiles));
                    })
                    ->getOptionLabelUsing(fn ($value): string => pathinfo($value, PATHINFO_FILENAME)),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_name')
                    ->label('Image')
                    ->getStateUsing(function ($record) {
                        return Storage::disk('s3')->url("Product/" . $record->image_name);
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductImages::route('/'),
            'create' => Pages\CreateProductImage::route('/create'),
            'edit' => Pages\EditProductImage::route('/{record}/edit'),
        ];
    }

    public static function getAncestor(): ?Ancestor
    {

        return Ancestor::make(
            'product_image',
            'product',
        );
    }
}
