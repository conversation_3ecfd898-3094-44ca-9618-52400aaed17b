<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('business_contacts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('business_name')->nullable();
            $table->string('business_phone')->nullable();
            $table->string('business_owner_name')->nullable();
            $table->string('business_address')->nullable();
            $table->string('lead_source')->nullable();
            $table->string('notes')->nullable();
            $table->string('interest_status')->default('not_confirmed');
            $table->timestamps();
            $table->softDeletes();
        });
    }
};
