<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ArtistResource;
use App\Filament\Resources\ProductResource;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\Pages\ManageRelatedRecords;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Guava\FilamentNestedResources\Concerns\NestedPage;
use Guava\FilamentNestedResources\Concerns\NestedRelationManager;
use Guava\FilamentNestedResources\Concerns\NestedResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;
use Storage;

class ManageProductImage extends ManageRelatedRecords
{
    use NestedPage;
    use NestedRelationManager;

    protected static string $resource = ProductResource::class;

    protected static string $relationship = 'product_image';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationLabel(): string
    {
        return 'Images';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('image_name')
                    ->image()
                    ->required()
                    ->disk('s3')
                    ->getUploadedFileNameForStorageUsing(function ($file) {
                        // dd($file);
                        return LaradrobeServices::uploadProductImage($file);
                    }),
                
                Forms\Components\Hidden::make('user_id')
                    ->default(fn (RelationManager $livewire) => $livewire->getOwnerRecord()->user_id),
    
                Forms\Components\Hidden::make('product_id')
                    ->default(fn (RelationManager $livewire) => $livewire->getOwnerRecord()->id),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('Image')
            ->columns([
                Tables\Columns\ImageColumn::make('image_name')
                    ->label('Image')
                    ->getStateUsing(function ($record) {
                        return Storage::disk('s3')->url("Product/" . $record->image_name);
                    }),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
