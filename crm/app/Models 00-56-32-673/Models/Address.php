<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Address extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'id',
        'user_id',
        'lat',
        'lng',
        'locality',
        'sub_locality',
        'administrative_area',
        'country',
        'postal_code',
        'address_type',
    ];
}
