<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use Guava\FilamentNestedResources\Concerns\NestedPage;
use Guava\FilamentNestedResources\Pages\CreateRelatedRecord;

class CreateProductImage extends CreateRelatedRecord
{
    use NestedPage;

    protected static string $resource = ProductResource::class;

    protected static string $relationship = 'product_image';
}
