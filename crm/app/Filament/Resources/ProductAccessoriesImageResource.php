<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductAccessoriesImageResource\Pages;
use App\Filament\Resources\ProductAccessoriesImageResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Guava\FilamentNestedResources\Ancestor;
use Guava\FilamentNestedResources\Concerns\NestedResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;
use MrBohem\LaradrobeServices\Models\ProductAccessory;
use MrBohem\LaradrobeServices\Models\ProductAccessoryImage;
use Storage;

class ProductAccessoriesImageResource extends Resource
{
    use NestedResource;
    protected static ?string $model = ProductAccessoryImage::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('image_name')
                    ->image()
                    ->disk('s3')
                    ->getUploadedFileNameForStorageUsing(function ($file) {
                        // dd($file);
                        return LaradrobeServices::uploadProductImage($file);
                    }),
                
                    Forms\Components\Hidden::make('product_id')
                    ->default( function() {
                        $productAccessoryId = collect(explode('/', url()->current()))->get(5);
                        $productAccessory = ProductAccessory::where('id', $productAccessoryId)->first();
                        return $productAccessory->product_id;
                    }),

                Forms\Components\Hidden::make('product_accessory_id')
                    ->default(collect(explode('/', url()->current()))->get(5)),

                // Forms\Components\Select::make('image_name')
                //     // ->options(Storage::disk('s3')->allFiles('TempImage'))
                //     ->getSearchResultsUsing(fn (string $search) => 
                //         Storage::disk('s3')->allFiles('TempImage')
                //     )
                //     ->getOptionLabelUsing(function ($value) {
                //         dd(Storage::disk('s3')->allFiles('TempImage'));
                //         return $value;
                //     })
                //     ->searchable(),

                // Forms\Components\TextInput::make('image_name1')
                // ->afterStateUpdated(function (?string $state, callable $set,callable $get) {
                //     if($state == null) return;
                //     $filePath = 'TempImage/' . $state;
                //     $fileName = LaradrobeServices::attachImageFromTemp($filePath);
                //     $tempArr = [$fileName => $fileName];
                //     $set('image_name', $tempArr);
                // }),

                Forms\Components\Select::make('image_name1')
                    ->label('Select Image')
                    ->options(function () {
                        $files = Storage::disk('s3')->files('TempImage');
                        $imageFiles = array_filter($files, function ($file) {
                            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);
                        });

                        return array_combine($imageFiles, array_map(function ($file) {
                            return pathinfo($file, PATHINFO_FILENAME); // Display only the filename in the select box
                        }, $imageFiles));
                    })
                    ->searchable() // Optional: Add search functionality if you have many images
                    ->afterStateUpdated(function (?string $state, callable $set, callable $get) {
                        if ($state == null) {
                            return;
                        }
                        $fileName = LaradrobeServices::attachImageFromTemp($state); // Modify your service to handle S3 paths
                        $tempArr = [$fileName => $fileName];
                        $set('image_name', $tempArr);
                    })
                    ->getSearchResultsUsing(function (string $search)  { // Corrected line: Added 'use ($folderPath)'
                        $files = Storage::disk('s3')->files('TempImage');
                        $imageFiles = array_filter($files, function ($file) use ($search) { // Corrected line: Added 'use ($search)'
                            return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']) &&
                                   str_contains(pathinfo($file, PATHINFO_FILENAME), $search);
                        });
                
                        return array_combine($imageFiles, array_map(function ($file) {
                            return pathinfo($file, PATHINFO_FILENAME);
                        }, $imageFiles));
                    })
                    ->getOptionLabelUsing(fn ($value): string => pathinfo($value, PATHINFO_FILENAME)),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_name')
                ->label('Image')
                ->getStateUsing(function ($record) {
                    return Storage::disk('s3')->url("Product/" . $record->image_name);
                }),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductAccessoriesImages::route('/'),
            // 'create' => Pages\CreateProductAccessoriesImage::route('/create'),
            // 'edit' => Pages\EditProductAccessoriesImage::route('/{record}/edit'),
        ];
    }

    public static function getAncestor(): ?Ancestor
    {

        return Ancestor::make(
            'images',
            'accessory',
        );
    }
}
