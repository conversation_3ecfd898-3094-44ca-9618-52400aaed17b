<x-filament::page>
    <h2 class="text-xl font-bold mb-6">Temp Images</h2>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        @foreach ($this->images as $image)
            <div class="border rounded-lg shadow-sm overflow-hidden p-2 bg-white">
                <div class="h-48 overflow-hidden flex items-center justify-center bg-gray-100">
                    <img src="{{ Storage::disk('s3')->url($image) }}" alt="Image"
                         class="max-h-48 w-auto object-contain" />
                </div>

                <div class="mt-2 text-center text-sm break-all text-gray-700">
                    {{ basename($image) }}
                </div>

                <div class="mt-2 text-center flex justify-center gap-2">
                    <button wire:click="removeBackground('{{ basename($image)  }}')"
                        class="px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-700">
                        Remove BG
                    </button>

                    <button wire:click="deleteImage('{{ basename($image) }}')"
                    style="--c-400:var(--danger-400);--c-500:var(--danger-500);--c-600:var(--danger-600);"
                        class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-danger fi-color-danger fi-size-md fi-btn-size-md gap-1.5 px-3 py-2 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action">
                        Delete
                    </button>
                </div>
            </div>
        @endforeach
    </div>
</x-filament::page>
