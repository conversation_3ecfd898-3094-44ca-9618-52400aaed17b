<?php

namespace App\Filament\Resources\ProductAccessoriesImageResource\Pages;

use App\Filament\Resources\ProductAccessoriesImageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Guava\FilamentNestedResources\Concerns\NestedPage;

class EditProductAccessoriesImage extends EditRecord
{
    use NestedPage;
    protected static string $resource = ProductAccessoriesImageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
