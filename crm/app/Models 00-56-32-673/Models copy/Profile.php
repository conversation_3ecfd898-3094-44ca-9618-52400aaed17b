<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory,HasUuids;

    protected $fillable = [
        'user_id',
        'age',
        'address',
        'phone',
        'city'
    ];

    protected function city():Attribute
    {
        return Attribute::make(
            set: fn (string $value) => ucwords(strtolower($value)),
        );
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
