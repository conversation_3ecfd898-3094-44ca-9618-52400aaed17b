<?php

namespace Tests\Unit\Services;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mr<PERSON><PERSON><PERSON>\LaradrobeServices\ProductMaintenanceService;
use MrBohem\LaradrobeServices\Models\Product;
use MrBohem\LaradrobeServices\Models\ProductMaintenanceStatus;
use App\ProductVendorAssignment;
use App\Vendor;
use App\User;

class ProductMaintenanceServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->product = Product::factory()->create();
    }

    /** @test */
    public function it_can_update_product_status_manually()
    {
        $result = ProductMaintenanceService::updateStatusManually(
            $this->product->id,
            'dirty',
            'Product needs cleaning',
            $this->user->id
        );

        $this->assertInstanceOf(ProductMaintenanceStatus::class, $result);
        $this->assertEquals('needs_cleaning', $result->maintenance_status);
        $this->assertEquals('Product needs cleaning', $result->notes);
        $this->assertEquals($this->user->id, $result->updated_by);
        $this->assertEquals('manual', $result->source);
    }

    /** @test */
    public function it_can_get_current_status()
    {
        // Create multiple status records
        ProductMaintenanceService::updateStatusManually($this->product->id, 'dirty', 'First', $this->user->id);
        sleep(1);
        ProductMaintenanceService::updateStatusManually($this->product->id, 'clean', 'Latest', $this->user->id);

        $currentStatus = ProductMaintenanceService::getCurrentStatus($this->product->id);

        $this->assertEquals('ready', $currentStatus['maintenance_status']);
        $this->assertTrue($currentStatus['is_clean']);
        $this->assertEquals('Latest', $currentStatus['notes']);
    }

    /** @test */
    public function it_returns_default_status_for_new_product()
    {
        $newProduct = Product::factory()->create();
        
        $status = ProductMaintenanceService::getCurrentStatus($newProduct->id);

        $this->assertEquals('ready', $status['maintenance_status']);
        $this->assertTrue($status['is_clean']);
    }

    /** @test */
    public function it_can_get_products_by_status()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();

        // Set different statuses
        ProductMaintenanceService::updateStatusManually($product1->id, 'dirty', 'Test', $this->user->id);
        ProductMaintenanceService::updateStatusManually($product2->id, 'damaged', 'Test', $this->user->id);
        ProductMaintenanceService::updateStatusManually($product3->id, 'clean', 'Test', $this->user->id);

        $needsCleaning = ProductMaintenanceService::getProductsByStatus('needs_cleaning');
        $damaged = ProductMaintenanceService::getProductsByStatus('damaged');
        $cleanReady = ProductMaintenanceService::getProductsByStatus('clean_ready');

        $this->assertContains($product1->id, $needsCleaning);
        $this->assertContains($product2->id, $damaged);
        $this->assertContains($product3->id, $cleanReady);
    }

    /** @test */
    public function it_filters_out_assigned_products()
    {
        $vendor = Vendor::factory()->create();
        
        // Mark product as dirty
        ProductMaintenanceService::updateStatusManually($this->product->id, 'dirty', 'Test', $this->user->id);
        
        // Assign to vendor
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'assigned',
            'assigned_date' => now()->toDateString(),
            'assigned_by' => $this->user->id,
        ]);

        $needsCleaning = ProductMaintenanceService::getProductsByStatus('needs_cleaning');
        $needsAttention = ProductMaintenanceService::getProductsByStatus('needs_attention');

        $this->assertNotContains($this->product->id, $needsCleaning);
        $this->assertNotContains($this->product->id, $needsAttention);
    }

    /** @test */
    public function it_filters_out_recently_returned_products()
    {
        $vendor = Vendor::factory()->create();
        
        // Mark product as dirty
        ProductMaintenanceService::updateStatusManually($this->product->id, 'dirty', 'Test', $this->user->id);
        
        // Create recent return (2 hours ago)
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'returned',
            'assigned_date' => now()->subHours(6)->toDateString(),
            'actual_return_date' => now()->subHours(2)->toDateString(),
            'assigned_by' => $this->user->id,
            'returned_by' => $this->user->id,
        ]);

        $needsAttention = ProductMaintenanceService::getProductsByStatus('needs_attention');

        $this->assertNotContains($this->product->id, $needsAttention);
    }

    /** @test */
    public function it_includes_products_returned_more_than_4_hours_ago()
    {
        $vendor = Vendor::factory()->create();
        
        // Mark product as dirty
        ProductMaintenanceService::updateStatusManually($this->product->id, 'dirty', 'Test', $this->user->id);
        
        // Create old return (6 hours ago)
        ProductVendorAssignment::create([
            'product_id' => $this->product->id,
            'vendor_id' => $vendor->id,
            'assignment_type' => 'cleaning',
            'status' => 'returned',
            'assigned_date' => now()->subHours(10)->toDateString(),
            'actual_return_date' => now()->subHours(6)->toDateString(),
            'assigned_by' => $this->user->id,
            'returned_by' => $this->user->id,
        ]);

        $needsAttention = ProductMaintenanceService::getProductsByStatus('needs_attention');

        $this->assertContains($this->product->id, $needsAttention);
    }

    /** @test */
    public function it_only_considers_latest_status_record()
    {
        // Create multiple status records with different statuses
        ProductMaintenanceService::updateStatusManually($this->product->id, 'dirty', 'Old status', $this->user->id);
        sleep(1);
        ProductMaintenanceService::updateStatusManually($this->product->id, 'clean', 'Latest status', $this->user->id);

        $needsCleaning = ProductMaintenanceService::getProductsByStatus('needs_cleaning');
        $cleanReady = ProductMaintenanceService::getProductsByStatus('clean_ready');

        // Should not be in needs_cleaning because latest status is clean
        $this->assertNotContains($this->product->id, $needsCleaning);
        // Should be in clean_ready because latest status is clean
        $this->assertContains($this->product->id, $cleanReady);
    }

    /** @test */
    public function it_combines_multiple_statuses_for_needs_attention()
    {
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        $product3 = Product::factory()->create();
        $product4 = Product::factory()->create();

        // Set different statuses that need attention
        ProductMaintenanceService::updateStatusManually($product1->id, 'dirty', 'Test', $this->user->id);
        ProductMaintenanceService::updateStatusManually($product2->id, 'damaged', 'Test', $this->user->id);
        ProductMaintenanceService::updateStatusManually($product3->id, 'clean', 'Test', $this->user->id); // Should not be included
        
        $needsAttention = ProductMaintenanceService::getProductsByStatus('needs_attention');

        $this->assertContains($product1->id, $needsAttention);
        $this->assertContains($product2->id, $needsAttention);
        $this->assertNotContains($product3->id, $needsAttention);
    }

    /** @test */
    public function it_validates_status_mapping()
    {
        $statusMapping = ProductMaintenanceService::getStatusMapping();

        // Check that all required statuses exist
        $requiredStatuses = ['ready', 'clean', 'dirty', 'needs_cleaning', 'damaged', 'under_cleaning', 'under_repair'];
        
        foreach ($requiredStatuses as $status) {
            $this->assertArrayHasKey($status, $statusMapping);
            $this->assertArrayHasKey('maintenance_status', $statusMapping[$status]);
            $this->assertArrayHasKey('label', $statusMapping[$status]);
            $this->assertArrayHasKey('color', $statusMapping[$status]);
            $this->assertArrayHasKey('description', $statusMapping[$status]);
        }
    }

    /** @test */
    public function it_gets_correct_status_info()
    {
        $statusInfo = ProductMaintenanceService::getStatusInfo('needs_cleaning');

        $this->assertEquals('needs_cleaning', $statusInfo['maintenance_status']);
        $this->assertEquals('Needs Cleaning', $statusInfo['label']);
        $this->assertEquals('orange', $statusInfo['color']);
        $this->assertFalse($statusInfo['is_clean']);
    }

    /** @test */
    public function it_returns_default_status_info_for_unknown_status()
    {
        $statusInfo = ProductMaintenanceService::getStatusInfo('unknown_status');

        $this->assertEquals('ready', $statusInfo['maintenance_status']);
        $this->assertEquals('Ready', $statusInfo['label']);
        $this->assertEquals('green', $statusInfo['color']);
    }
}
