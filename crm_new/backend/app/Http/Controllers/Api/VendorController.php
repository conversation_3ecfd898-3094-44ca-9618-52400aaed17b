<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Mr<PERSON><PERSON>em\LaradrobeServices\Models\Vendor;
use MrB<PERSON>em\LaradrobeServices\VendorService;
use MrBohem\LaradrobeServices\Enums\VendorAssignmentStatus;

class VendorController extends Controller
{
    /**
     * Get all vendors
     */
    public function index(Request $request)
    {
        $query = Vendor::query();

        // Filter by type if provided
        if ($request->has('type')) {
            $query->byType($request->type);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        } else {
            // Default to active vendors only
            $query->active();
        }

        $vendors = $query->withCount(['assignments', 'activeAssignments'])
                        ->orderBy('name')
                        ->get();

        return response()->json([
            'vendors' => $vendors
        ]);
    }

    /**
     * Store a new vendor
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:dry_cleaner,tailor,repair_shop',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'rate_per_item' => 'nullable|numeric|min:0',
            'specialties' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        $vendor = Vendor::create($request->all());

        return response()->json([
            'message' => 'Vendor created successfully',
            'vendor' => $vendor
        ], 201);
    }

    /**
     * Get vendor details with assignments
     */
    public function show($id)
    {
        $vendor = Vendor::with(['assignments.product', 'activeAssignments.product'])
                       ->findOrFail($id);

        $stats = $vendor->getSummaryStats();

        return response()->json([
            'vendor' => $vendor,
            'stats' => $stats
        ]);
    }

    /**
     * Update vendor
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:dry_cleaner,tailor,repair_shop',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'rate_per_item' => 'nullable|numeric|min:0',
            'specialties' => 'nullable|string',
            'status' => 'required|in:active,inactive',
            'notes' => 'nullable|string'
        ]);

        $vendor = Vendor::findOrFail($id);
        $vendor->update($request->all());

        return response()->json([
            'message' => 'Vendor updated successfully',
            'vendor' => $vendor
        ]);
    }

    /**
     * Delete vendor (soft delete by marking inactive)
     */
    public function destroy($id)
    {
        $vendor = Vendor::findOrFail($id);

        // Check if vendor has active assignments
        if ($vendor->activeAssignments()->count() > 0) {
            return response()->json([
                'message' => 'Cannot delete vendor with active assignments',
                'error' => true
            ], 400);
        }

        $vendor->update(['status' => 'inactive']);

        return response()->json([
            'message' => 'Vendor deactivated successfully'
        ]);
    }

    /**
     * Assign product to vendor
     */
    public function assignProduct(Request $request)
    {
        $request->validate([
            'product_id' => 'required|string',
            'vendor_id' => 'required|exists:vendors,id',
            'assignment_type' => 'required|in:cleaning,repair,alteration,maintenance',
            'expected_return_date' => 'nullable|date|after:today',
            'cost' => 'nullable|numeric|min:0',
            'work_description' => 'nullable|string',
            'internal_notes' => 'nullable|string'
        ]);

        try {
            $assignment = VendorService::assignProductToVendor(
                $request->product_id,
                $request->vendor_id,
                $request->all()
            );

            return response()->json([
                'message' => 'Product assigned to vendor successfully',
                'assignment' => $assignment->load(['vendor', 'product'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'error' => true
            ], 400);
        }
    }

    /**
     * Mark product as returned from vendor
     */
    public function markProductReturned(Request $request, $assignmentId)
    {
        $request->validate([
            'actual_return_date' => 'nullable|date',
            'vendor_notes' => 'nullable|string',
            'cost' => 'nullable|numeric|min:0'
        ]);

        try {
            $assignment = VendorService::markProductAsReturned(
                $assignmentId,
                $request->all()
            );

            return response()->json([
                'message' => 'Product marked as returned successfully',
                'assignment' => $assignment->load(['vendor', 'product'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'error' => true
            ], 400);
        }
    }

    /**
     * Get vendor dashboard data
     */
    public function dashboard(Request $request)
    {
        $vendorId = $request->get('vendor_id');
        $dashboard = VendorService::getVendorDashboard($vendorId);

        return response()->json($dashboard);
    }

    /**
     * Get products currently with vendors
     */
    public function productsWithVendors()
    {
        $products = VendorService::getProductsWithVendors();

        return response()->json([
            'assignments' => $products
        ]);
    }

    /**
     * Get overdue assignments
     */
    public function overdueAssignments()
    {
        $overdue = VendorService::getOverdueAssignments();

        return response()->json([
            'overdue_assignments' => $overdue
        ]);
    }
}
