import React, { useState, useEffect } from 'react';
import { X, Check, Image, RefreshCw, Search } from 'lucide-react';
import { productImagesAPI } from '../services/api';

const TempImageSelector = ({ isOpen, onClose, onSelect, maxSelection = 5 }) => {
  const [tempImages, setTempImages] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadTempImages();
    }
  }, [isOpen]);

  const loadTempImages = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await productImagesAPI.getTempImages();
      setTempImages(response.temp_images || []);
    } catch (error) {
      console.error('Error loading temp images:', error);
      setError('Failed to load temp images');
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = (image) => {
    if (selectedImages.find(img => img.filename === image.filename)) {
      // Remove from selection
      setSelectedImages(prev => prev.filter(img => img.filename !== image.filename));
    } else {
      // Add to selection (check max limit)
      if (selectedImages.length < maxSelection) {
        setSelectedImages(prev => [...prev, image]);
      }
    }
  };

  const handleConfirmSelection = () => {
    onSelect(selectedImages.map(img => img.filename));
    handleClose();
  };

  const handleClose = () => {
    setSelectedImages([]);
    setSearchTerm('');
    onClose();
  };

  const filteredImages = tempImages.filter(image =>
    image.filename.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '80vh',
        overflow: 'hidden',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
      }}>
        {/* Header */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#1f2937',
              margin: 0
            }}>
              Select from Temp Images
            </h2>
            <p style={{
              fontSize: '14px',
              color: '#6b7280',
              margin: '4px 0 0 0'
            }}>
              Choose up to {maxSelection} images from the temp folder
            </p>
          </div>
          <button
            onClick={handleClose}
            style={{
              padding: '8px',
              border: 'none',
              backgroundColor: 'transparent',
              cursor: 'pointer',
              borderRadius: '6px'
            }}
          >
            <X size={20} color="#6b7280" />
          </button>
        </div>

        {/* Search and Refresh */}
        <div style={{
          padding: '16px 20px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          gap: '12px',
          alignItems: 'center'
        }}>
          <div style={{ position: 'relative', flex: 1 }}>
            <Search size={16} style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#6b7280'
            }} />
            <input
              type="text"
              placeholder="Search images..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '8px 12px 8px 36px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px'
              }}
            />
          </div>
          <button
            onClick={loadTempImages}
            disabled={loading}
            style={{
              padding: '8px 12px',
              backgroundColor: '#f3f4f6',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              fontSize: '14px'
            }}
          >
            <RefreshCw size={14} style={{
              animation: loading ? 'spin 1s linear infinite' : 'none'
            }} />
            Refresh
          </button>
        </div>

        {/* Content */}
        <div style={{
          padding: '20px',
          maxHeight: '400px',
          overflowY: 'auto'
        }}>
          {loading ? (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#6b7280'
            }}>
              <RefreshCw size={32} style={{
                animation: 'spin 1s linear infinite',
                margin: '0 auto 16px'
              }} />
              <p>Loading temp images...</p>
            </div>
          ) : error ? (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#dc2626'
            }}>
              <p>{error}</p>
              <button
                onClick={loadTempImages}
                style={{
                  marginTop: '12px',
                  padding: '8px 16px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                Retry
              </button>
            </div>
          ) : filteredImages.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '40px',
              color: '#6b7280'
            }}>
              <Image size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
              <p>No temp images found</p>
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
              gap: '12px'
            }}>
              {filteredImages.map((image) => {
                const isSelected = selectedImages.find(img => img.filename === image.filename);
                return (
                  <div
                    key={image.filename}
                    onClick={() => handleImageSelect(image)}
                    style={{
                      position: 'relative',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      cursor: 'pointer',
                      border: isSelected ? '2px solid #3b82f6' : '2px solid transparent',
                      transition: 'all 0.2s'
                    }}
                  >
                    <img
                      src={image.url}
                      alt={image.filename}
                      style={{
                        width: '100%',
                        height: '120px',
                        objectFit: 'cover'
                      }}
                    />
                    
                    {/* Selection indicator */}
                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '4px',
                        right: '4px',
                        width: '20px',
                        height: '20px',
                        backgroundColor: '#3b82f6',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <Check size={12} color="white" />
                      </div>
                    )}
                    
                    {/* Image info */}
                    <div style={{
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                      color: 'white',
                      padding: '8px 6px 4px',
                      fontSize: '10px'
                    }}>
                      <div style={{ fontWeight: '500', marginBottom: '2px' }}>
                        {image.filename.length > 15 ? 
                          image.filename.substring(0, 12) + '...' : 
                          image.filename
                        }
                      </div>
                      <div style={{ opacity: 0.8 }}>
                        {formatFileSize(image.size)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div style={{
          padding: '16px 20px',
          borderTop: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            {selectedImages.length} of {maxSelection} selected
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={handleClose}
              style={{
                padding: '8px 16px',
                border: '1px solid #d1d5db',
                backgroundColor: 'white',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleConfirmSelection}
              disabled={selectedImages.length === 0}
              style={{
                padding: '8px 16px',
                backgroundColor: selectedImages.length > 0 ? '#3b82f6' : '#9ca3af',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: selectedImages.length > 0 ? 'pointer' : 'not-allowed',
                fontSize: '14px'
              }}
            >
              Select {selectedImages.length} Image{selectedImages.length !== 1 ? 's' : ''}
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default TempImageSelector;
