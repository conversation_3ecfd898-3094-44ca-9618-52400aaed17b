<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use MrBohem\LaradrobeServices\Facades\LaradrobeServices;
use <PERSON><PERSON><PERSON><PERSON>\LaradrobeServices\Models\Product;
use MrB<PERSON>em\LaradrobeServices\Models\ProductAccessory;
use MrBohem\LaradrobeServices\Models\ProductAccessoryImage;

class AccessoryImageController extends Controller
{
    public function index(Product $product, ProductAccessory $accessory)
    {
        // Ensure the accessory belongs to the product
        if ($accessory->product_id !== $product->id) {
            return response()->json([
                'message' => 'Accessory not found for this product'
            ], 404);
        }

        // Get images from the product_accessory_images table
        $images = ProductAccessoryImage::where('product_id', $product->id)
            ->where('product_accessory_id', $accessory->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'images' => $images
        ]);
    }

    public function store(Request $request, Product $product, ProductAccessory $accessory)
    {
        // Ensure the accessory belongs to the product
        if ($accessory->product_id !== $product->id) {
            return response()->json([
                'message' => 'Accessory not found for this product'
            ], 404);
        }

        // Validate based on upload type
        if ($request->has('upload_type') && $request->upload_type === 'temp') {
            // Validate for temp image selection
            $request->validate([
                'temp_images' => 'required|array|max:5',
                'temp_images.*' => 'required|string', // Image names from temp folder
                'upload_type' => 'required|string|in:temp,file'
            ]);
        } else {
            // Validate for file upload
            $request->validate([
                'images' => 'required|array|max:5',
                'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
                'upload_type' => 'sometimes|string|in:temp,file'
            ]);
        }

        try {
            $uploadedImages = [];

            if ($request->has('upload_type') && $request->upload_type === 'temp') {
                // Handle temp image attachment
                foreach ($request->temp_images as $tempImageName) {
                    // Attach image from temp folder using LaradrobeServices
                    $uploadResult = LaradrobeServices::attachImageFromTemp('TempImage/'.$tempImageName);

                    if ($uploadResult) {
                        // Create database record in product_accessory_images table
                        $accessoryImage = ProductAccessoryImage::create([
                            'product_id' => $product->id,
                            'product_accessory_id' => $accessory->id,
                            'image_name' => $uploadResult,
                        ]);

                        $uploadedImages[] = $accessoryImage;
                    }
                }
            } else {
                // Handle file upload (existing functionality)
                foreach ($request->file('images') as $image) {
                    // Store image using LaradrobeServices
                    $filename = LaradrobeServices::uploadProductImage($image);

                    // Create database record in product_accessory_images table
                    $accessoryImage = ProductAccessoryImage::create([
                        'product_id' => $product->id,
                        'product_accessory_id' => $accessory->id,
                        'image_name' => $filename,
                    ]);

                    $uploadedImages[] = $accessoryImage;
                }
            }

            $uploadType = $request->upload_type === 'temp' ? 'attached from temp folder' : 'uploaded';

            return response()->json([
                'message' => "Images {$uploadType} successfully",
                'images' => $uploadedImages,
                'upload_type' => $request->upload_type ?? 'file'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to process images: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Product $product, ProductAccessory $accessory, ProductAccessoryImage $image)
    {
        // Ensure the accessory belongs to the product
        if ($accessory->product_id !== $product->id) {
            return response()->json([
                'message' => 'Accessory not found for this product'
            ], 404);
        }

        // Ensure the image belongs to the accessory
        if ($image->product_accessory_id !== $accessory->id || $image->product_id !== $product->id) {
            return response()->json([
                'message' => 'Image not found for this accessory'
            ], 404);
        }

        try {
            // Delete the physical file
            $imagePath = 'accessory_images/' . $image->image_name;
            if (Storage::disk('public')->exists($imagePath)) {
                Storage::disk('public')->delete($imagePath);
            }

            // Delete database record
            $image->delete();

            return response()->json([
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }
}
