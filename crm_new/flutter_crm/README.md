# Flutter CRM App

A comprehensive Customer Relationship Management (CRM) mobile application built with Flutter, designed to work seamlessly with the Laravel CRM backend.

## Features

### ✅ **Implemented Features**

#### **Authentication**
- Login with email and password
- Secure token-based authentication
- Auto-login with stored credentials
- Logout functionality

#### **Dashboard**
- Welcome section with gradient design
- Statistics cards showing:
  - Total Products
  - Total Orders
  - Total Users
  - Total Vendors
- Quick action buttons
- Recent activity section (placeholder)

#### **Products Management**
- View all products with search functionality
- Product cards with images, details, and pricing
- **Product Timeline** - Complete product lifecycle tracking:
  - Product registration
  - Order events (creation, pickup, delivery, return)
  - Return processing with condition tracking
  - Maintenance status changes
  - Vendor assignments and returns
- Timeline filtering by event type
- Statistics dashboard for each product
- Refresh functionality

#### **Users Management**
- View all users with search functionality
- User cards with contact information
- User profile avatars

#### **Orders Management**
- View all orders with search functionality
- Order cards with status indicators
- Customer and product information
- Date ranges and pricing

#### **Vendors Management**
- View all vendors with search functionality
- Vendor cards with service types
- Contact information and addresses

#### **Navigation**
- Responsive drawer navigation
- Route-based navigation with GoRouter
- User profile menu with logout

### **Technical Features**
- **State Management**: Provider pattern
- **HTTP Client**: Custom API service with error handling
- **Image Caching**: Cached network images
- **Local Storage**: SharedPreferences for auth persistence
- **Responsive Design**: Adaptive layouts for different screen sizes
- **Material Design 3**: Modern UI components

## Setup Instructions

### **Prerequisites**
- Flutter SDK (3.0+)
- Android Studio / VS Code
- Android emulator or physical device
- Laravel CRM backend running

### **Installation**

1. **Install dependencies**:
   ```bash
   flutter pub get
   ```

2. **Update API configuration**:
   - Edit `lib/services/api_service.dart`
   - Update `baseUrl` to match your backend URL

3. **Run the app**:
   ```bash
   flutter run
   ```

### **Building APK**
```bash
flutter build apk --release
```

## Demo Credentials

Use these credentials to test the app:
- **Email**: `<EMAIL>`
- **Password**: `password`

## Key Features

### **Product Timeline**
The standout feature of this CRM app is the comprehensive product timeline that tracks:

1. **Product Registration** - When product is first added
2. **Order Lifecycle** - Creation → Pickup → Delivery → Return
3. **Return Processing** - Detailed condition tracking
4. **Maintenance Status** - Clean/dirty/damaged status changes
5. **Vendor Work** - Assignments to dry cleaners/tailors
6. **Vendor Returns** - When products come back from vendors

**Timeline Features**:
- ✅ Chronological event ordering
- ✅ Event filtering by type
- ✅ Rich event details with formatted data
- ✅ Statistics dashboard
- ✅ Visual timeline with icons and colors
- ✅ Proper date formatting

## API Integration

The app connects to the Laravel CRM backend at:
- **Development**: `http://10.0.2.2:8001/api` (Android emulator)
- **Production**: Update `baseUrl` in `lib/services/api_service.dart`
