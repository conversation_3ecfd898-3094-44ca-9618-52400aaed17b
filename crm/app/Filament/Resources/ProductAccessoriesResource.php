<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductAccessoriesResource\Pages;
use App\Filament\Resources\ProductAccessoriesResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Guava\FilamentNestedResources\Ancestor;
use Guava\FilamentNestedResources\Concerns\NestedResource;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use MrBohem\LaradrobeServices\Models\ProductAccessory;

class ProductAccessoriesResource extends Resource
{
    use NestedResource;
    protected static ?string $model = ProductAccessory::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Forms\Components\TextInput::make('accessory')
                ->required()
                ->maxLength(255),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('accessory')
                    ->label('Accessory')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductAccessories::route('/'),
            'create' => Pages\CreateProductAccessories::route('/create'),
            'edit' => Pages\EditProductAccessories::route('/{record}/edit'),

            'images' => Pages\ManageProductAccessoriesImage::route('/{record}/productAccessoriesImage'),
            'images.create' => Pages\CreateProductAccessoriesImage::route('/{record}/productAccessoriesImage/create'),

        ];
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\EditProductAccessories::class,
            Pages\ManageProductAccessoriesImage::class,
        ]);
    }

    public static function getAncestor(): ?Ancestor
    {
        $url = url()->current();
        $snapshotJson = request()->input('components.0.snapshot');
        if ($snapshotJson) {
            $snapshot = json_decode($snapshotJson, true);
            if(str_contains($snapshot['memo']['path'],'admin/products/9ca7539e-3490-4e08-9517-608062a4b3f2/productAccessories')){
                return Ancestor::make(
                    'productAccessories',
                    'products',
                );
            }
        }

        if((str_contains($url,'productAccessories') && 
            !str_contains($url,'productAccessoriesImage'))
        ){
            return Ancestor::make(
                'productAccessories',
                'products',
            );
        }

        return null;
    }
}