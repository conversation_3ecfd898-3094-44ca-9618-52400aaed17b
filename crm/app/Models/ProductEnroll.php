<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductEnroll extends Model
{
    use SoftDeletes,HasUuids;
    protected $fillable = [
        'product_id',
        'has_cover',
        'has_hanger',
        'is_clean',
    ];

    public function product(){
        return $this->belongsTo(Product::class);
    }
}
